FROM nginx:1.25-alpine

WORKDIR /usr/share/nginx/html

# Remove default content
RUN rm -rf ./*

# Copy static assets
COPY . .

# Basic health file
RUN echo '{"status":"ok"}' > /usr/share/nginx/html/health

EXPOSE 80

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost/health || exit 1

CMD ["nginx", "-g", "daemon off;"]
