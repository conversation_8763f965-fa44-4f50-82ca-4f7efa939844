/**
 * GridSpoke SEO Admin JavaScript
 */

(function($) {
    'use strict';
    
    // Initialize when document is ready
    $(document).ready(function() {
        initializeAdminInterface();
        initializeAuthenticationHandlers();
        initializeSyncHandlers();
        initializeToolsHandlers();
        initializeLogsHandlers();
    });
    
    /**
     * Initialize admin interface
     */
    function initializeAdminInterface() {
        // Show/hide password fields
        $('#show-password, #show-api-key').on('click', function() {
            var $input = $(this).prev('input');
            var type = $input.attr('type') === 'password' ? 'text' : 'password';
            var text = type === 'password' ? gridspoke_admin.show_text : gridspoke_admin.hide_text;
            
            $input.attr('type', type);
            $(this).text(text);
        });
        
        // Auto-save settings on change (with debounce)
        var saveTimeout;
        $('.gridspoke-auto-save').on('change', function() {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(function() {
                $('#submit').click();
            }, 1000);
        });
        
        // Initialize tooltips
        $('.gridspoke-tooltip').tooltip();
        
        // Initialize tabs if present
        $('.gridspoke-nav-tab').on('click', function(e) {
            e.preventDefault();
            var target = $(this).attr('href');
            
            $('.gridspoke-nav-tab').removeClass('nav-tab-active');
            $(this).addClass('nav-tab-active');
            
            $('.gridspoke-tab-content').hide();
            $(target).show();
        });
    }
    
    /**
     * Initialize authentication handlers
     */
    function initializeAuthenticationHandlers() {
        // Test authentication
        $('#test-authentication').on('click', function() {
            var $button = $(this);
            var $status = $('#auth-status');
            var email = $('#email').val();
            var password = $('#password').val();
            
            if (!email || !password) {
                $status.html('<span class="gridspoke-status-error">' + gridspoke_admin.email_password_required + '</span>');
                return;
            }
            
            $button.prop('disabled', true).text(gridspoke_admin.testing_text);
            $status.html('<span class="gridspoke-status-loading">' + gridspoke_admin.testing_text + '</span>');
            
            $.ajax({
                url: gridspoke_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'gridspoke_test_authentication',
                    nonce: gridspoke_admin.nonce,
                    email: email,
                    password: password
                },
                success: function(response) {
                    if (response.success) {
                        $status.html('<span class="gridspoke-status-success">' + response.data.message + '</span>');
                        
                        // Show user info if available
                        if (response.data.user) {
                            $status.append('<br><small>' + gridspoke_admin.logged_in_as + ' ' + response.data.user.email + '</small>');
                        }
                    } else {
                        $status.html('<span class="gridspoke-status-error">' + response.data.message + '</span>');
                    }
                },
                error: function() {
                    $status.html('<span class="gridspoke-status-error">' + gridspoke_admin.connection_error + '</span>');
                },
                complete: function() {
                    $button.prop('disabled', false).text(gridspoke_admin.test_auth_text);
                }
            });
        });
    }
    
    /**
     * Initialize sync handlers
     */
    function initializeSyncHandlers() {
        // Sync all products
        $('#sync-all-products').on('click', function() {
            var $button = $(this);
            var $result = $('#sync-result');
            var $progress = $('#sync-progress');
            
            if (!confirm(gridspoke_admin.confirm_sync_all)) {
                return;
            }
            
            $button.prop('disabled', true).text(gridspoke_admin.syncing_text);
            $result.hide();
            $progress.show();
            
            // Start progress animation
            var progress = 0;
            var progressInterval = setInterval(function() {
                progress += Math.random() * 10;
                if (progress > 90) progress = 90;
                updateProgress(progress);
            }, 500);
            
            $.ajax({
                url: gridspoke_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'gridspoke_sync_all_products',
                    nonce: gridspoke_admin.nonce
                },
                success: function(response) {
                    clearInterval(progressInterval);
                    updateProgress(100);
                    
                    setTimeout(function() {
                        $progress.hide();
                        
                        if (response.success) {
                            $result.removeClass('error').addClass('success')
                                .html('<p>' + response.data.message + '</p>')
                                .show();
                            
                            // Refresh stats if available
                            if (typeof refreshStats === 'function') {
                                refreshStats();
                            }
                        } else {
                            $result.removeClass('success').addClass('error')
                                .html('<p>' + response.data.message + '</p>')
                                .show();
                        }
                    }, 500);
                },
                error: function() {
                    clearInterval(progressInterval);
                    $progress.hide();
                    $result.removeClass('success').addClass('error')
                        .html('<p>' + gridspoke_admin.sync_error + '</p>')
                        .show();
                },
                complete: function() {
                    $button.prop('disabled', false).text(gridspoke_admin.sync_all_text);
                }
            });
        });
        
        // Selective sync
        $('#sync-selective').on('click', function() {
            var $button = $(this);
            var category = $('#sync-category').val();
            var dateFrom = $('#sync-date-from').val();
            var dateTo = $('#sync-date-to').val();
            
            $button.prop('disabled', true).text(gridspoke_admin.syncing_text);
            
            $.ajax({
                url: gridspoke_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'gridspoke_sync_selective_products',
                    nonce: gridspoke_admin.nonce,
                    category: category,
                    date_from: dateFrom,
                    date_to: dateTo
                },
                success: function(response) {
                    if (response.success) {
                        showNotice(response.data.message, 'success');
                    } else {
                        showNotice(response.data.message, 'error');
                    }
                },
                error: function() {
                    showNotice(gridspoke_admin.sync_error, 'error');
                },
                complete: function() {
                    $button.prop('disabled', false).text(gridspoke_admin.sync_selected_text);
                }
            });
        });
    }
    
    /**
     * Initialize tools handlers
     */
    function initializeToolsHandlers() {
        // Test webhook
        $('#test-webhook').on('click', function() {
            var $button = $(this);
            var $result = $('#webhook-test-result');
            
            $button.prop('disabled', true).text(gridspoke_admin.testing_text);
            $result.hide();
            
            $.ajax({
                url: gridspoke_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'gridspoke_test_webhook',
                    nonce: gridspoke_admin.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $result.removeClass('error').addClass('success')
                            .html('<p>' + response.data.message + '</p>')
                            .show();
                    } else {
                        $result.removeClass('success').addClass('error')
                            .html('<p>' + response.data.message + '</p>')
                            .show();
                    }
                },
                error: function() {
                    $result.removeClass('success').addClass('error')
                        .html('<p>' + gridspoke_admin.webhook_test_error + '</p>')
                        .show();
                },
                complete: function() {
                    $button.prop('disabled', false).text(gridspoke_admin.test_webhook_text);
                }
            });
        });
        
        // Bulk optimize
        $('#bulk-optimize').on('click', function() {
            var $button = $(this);
            var $result = $('#optimize-result');
            var fields = [];
            var priority = $('#optimize-priority').val();
            
            $('input[name="optimize-fields[]"]:checked').each(function() {
                fields.push($(this).val());
            });
            
            if (fields.length === 0) {
                showNotice(gridspoke_admin.select_fields_error, 'error');
                return;
            }
            
            if (!confirm(gridspoke_admin.confirm_bulk_optimize)) {
                return;
            }
            
            $button.prop('disabled', true).text(gridspoke_admin.optimizing_text);
            $result.hide();
            
            $.ajax({
                url: gridspoke_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'gridspoke_bulk_optimize',
                    nonce: gridspoke_admin.nonce,
                    fields: fields,
                    priority: priority
                },
                success: function(response) {
                    if (response.success) {
                        $result.removeClass('error').addClass('success')
                            .html('<p>' + response.data.message + '</p>')
                            .show();
                    } else {
                        $result.removeClass('success').addClass('error')
                            .html('<p>' + response.data.message + '</p>')
                            .show();
                    }
                },
                error: function() {
                    $result.removeClass('success').addClass('error')
                        .html('<p>' + gridspoke_admin.optimize_error + '</p>')
                        .show();
                },
                complete: function() {
                    $button.prop('disabled', false).text(gridspoke_admin.bulk_optimize_text);
                }
            });
        });
        
        // Run cleanup
        $('#run-cleanup').on('click', function() {
            var $button = $(this);
            var $result = $('#cleanup-result');
            var cleanupLogs = $('#cleanup-logs').is(':checked');
            var cleanupOptimizations = $('#cleanup-optimizations').is(':checked');
            
            if (!cleanupLogs && !cleanupOptimizations) {
                showNotice(gridspoke_admin.select_cleanup_options, 'error');
                return;
            }
            
            if (!confirm(gridspoke_admin.confirm_cleanup)) {
                return;
            }
            
            $button.prop('disabled', true).text(gridspoke_admin.cleaning_text);
            $result.hide();
            
            $.ajax({
                url: gridspoke_admin.ajax_url,
                type: 'POST',
                data: {
                    action: 'gridspoke_run_cleanup',
                    nonce: gridspoke_admin.nonce,
                    cleanup_logs: cleanupLogs,
                    cleanup_optimizations: cleanupOptimizations
                },
                success: function(response) {
                    if (response.success) {
                        $result.removeClass('error').addClass('success')
                            .html('<p>' + response.data.message + '</p>')
                            .show();
                    } else {
                        $result.removeClass('success').addClass('error')
                            .html('<p>' + response.data.message + '</p>')
                            .show();
                    }
                },
                error: function() {
                    $result.removeClass('success').addClass('error')
                        .html('<p>' + gridspoke_admin.cleanup_error + '</p>')
                        .show();
                },
                complete: function() {
                    $button.prop('disabled', false).text(gridspoke_admin.run_cleanup_text);
                }
            });
        });
    }
    
    /**
     * Initialize logs handlers
     */
    function initializeLogsHandlers() {
        // Auto-refresh logs every 30 seconds
        if ($('#recent-activity').length > 0) {
            setInterval(function() {
                refreshRecentActivity();
            }, 30000);
        }
    }
    
    /**
     * Update progress bar
     */
    function updateProgress(percent) {
        $('.gridspoke-progress-fill').css('width', percent + '%');
        $('.gridspoke-progress-text').text(Math.round(percent) + '%');
    }
    
    /**
     * Show admin notice
     */
    function showNotice(message, type) {
        var $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
        $('.wrap h1').after($notice);
        
        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            $notice.fadeOut();
        }, 5000);
    }
    
    /**
     * Refresh recent activity
     */
    function refreshRecentActivity() {
        $.ajax({
            url: gridspoke_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'gridspoke_get_recent_activity',
                nonce: gridspoke_admin.nonce
            },
            success: function(response) {
                if (response.success && response.data.html) {
                    $('#recent-activity').html(response.data.html);
                }
            }
        });
    }
    
    /**
     * Global refresh stats function (used by dashboard)
     */
    window.refreshStats = function() {
        $.ajax({
            url: gridspoke_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'gridspoke_get_optimization_stats',
                nonce: gridspoke_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('#completed-optimizations').text(response.data.completed_optimizations || 0);
                    $('#pending-optimizations').text(response.data.pending_optimizations || 0);
                    $('#failed-optimizations').text(response.data.failed_optimizations || 0);
                    $('#total-optimizations').text(response.data.total_optimizations || 0);
                }
            }
        });
    };
    
})(jQuery);
