/**
 * GridSpoke SEO Optimizer - Dashboard Main Module
 * Handles dashboard state, chart initialization, and tab management
 * CACHE BUSTER: 2024-01-01-15:30
 */

function dashboardApp() {
    return {
        // Dashboard state
        activeTab: 'dashboard',
        showNotifications: false,
        
        // User data
        user: {
            name: '',
            email: '',
            avatar: ''
        },

        // Dashboard statistics
        stats: {
            totalProducts: 0,
            optimizedProducts: 0,
            activeJobs: 0,
            averageSeoScore: 0
        },

        // Recent activity
        recentActivity: [],

        // Notifications
        notifications: [],

        // Settings
        settings: {
            apiBaseUrl: 'http://localhost:8000/api/v1',
            websocketUrl: 'ws://localhost:8000/ws',
            autoOptimize: false,
            emailNotifications: true
        },

        // Charts
        charts: {
            progressChart: null,
            jobStatusChart: null
        },

        /**
         * Initialize dashboard
         */
        async init() {
            const initId = Date.now() + '_' + Math.random();
            console.log(`🚀 DASHBOARD INIT CALLED [${initId}] - Entry point`);
            
            // Multiple safeguards to prevent repeated initialization
            if (window.dashboardInitialized || window.dashboardInitializing) {
                console.log(`⚠️ Dashboard already initialized/initializing [${initId}], skipping...`);
                return;
            }
            
            // Set initializing flag immediately
            window.dashboardInitializing = true;
            
            console.log(`🔄 Starting dashboard initialization [${initId}]...`);
            
            // Add try-catch to capture any errors
            try {
                // Immediate auth check with detailed logging
                console.log(`🔐 Checking authentication [${initId}]...`);
                
                // Check if Auth module exists
                if (typeof Auth === 'undefined') {
                    console.error(`❌ Auth module not available [${initId}]`);
                    throw new Error('Auth module not loaded');
                }
                
                console.log(`✅ Auth module available [${initId}]`);
                
                // Check authentication with timeout
                const isAuth = Auth.isAuthenticated();
                console.log(`🔍 Auth check result [${initId}]:`, isAuth);
                
                if (!isAuth) {
                    console.log(`🚪 User not authenticated [${initId}], redirecting to login...`);
                    // Set a flag to prevent multiple redirects
                    if (!sessionStorage.getItem('redirecting_to_login')) {
                        sessionStorage.setItem('redirecting_to_login', 'true');
                        window.location.href = 'login.html';
                    }
                    return;
                }
                
                console.log(`✅ User authenticated [${initId}], proceeding with dashboard...`);
            
            // Mark as fully initialized
            window.dashboardInitialized = true;
            window.dashboardInitializing = false;
            
            // Load user data
            await this.loadUserData();
            
            // Load dashboard data
            await this.loadDashboardData();
            
            // Initialize WebSocket connection
            this.initializeWebSocket();
            
            // Initialize charts after DOM is ready
            this.$nextTick(() => {
                this.initializeCharts();
            });

            // Setup auto-refresh
            this.setupAutoRefresh();
            
            console.log(`✅ Dashboard initialization complete [${initId}]`);
            
            } catch (error) {
                console.error(`🚨 ERROR in dashboard initialization [${initId}]:`, error);
                console.error('Error stack:', error.stack);
                window.dashboardInitializing = false;
                // On error, also redirect to login
                if (!sessionStorage.getItem('redirecting_to_login')) {
                    sessionStorage.setItem('redirecting_to_login', 'true');
                    window.location.href = 'login.html';
                }
            }
        },

        /**
         * Load user data
         */
        async loadUserData() {
            try {
                const user = Auth.getUser();
                if (user) {
                    this.user = user;
                }
            } catch (error) {
                console.error('Error loading user data:', error);
            }
        },

        /**
         * Load dashboard statistics and data
         */
        async loadDashboardData() {
            try {
                // Load stats in parallel
                const [stats, activity] = await Promise.all([
                    API.getStats(),
                    API.getRecentActivity({ limit: 10 })
                ]);

                this.stats = {
                    totalProducts: stats.total_products || 0,
                    optimizedProducts: stats.optimized_products || 0,
                    activeJobs: stats.active_jobs || 0,
                    averageSeoScore: Math.round(stats.average_seo_score || 0)
                };

                this.recentActivity = activity.items || activity || [];

            } catch (error) {
                console.error('Error loading dashboard data:', error);
                Utils.showToast('Failed to load dashboard data', 'error');
            }
        },

        /**
         * Initialize WebSocket connection
         */
        initializeWebSocket() {
            if (WebSocketClient && Auth.isAuthenticated()) {
                // Subscribe to dashboard updates
                WebSocketClient.subscribe('dashboard', (data) => {
                    this.handleWebSocketUpdate(data);
                });

                WebSocketClient.subscribe('notifications', (data) => {
                    this.addNotification(data);
                });

                // Initialize connection
                if (!WebSocketClient.getState().isConnected) {
                    WebSocketClient.init();
                }
            }
        },

        /**
         * Initialize dashboard charts
         */
        initializeCharts() {
            this.initializeProgressChart();
            this.initializeJobStatusChart();
        },

        /**
         * Initialize optimization progress chart
         */
        async initializeProgressChart() {
            const canvas = document.getElementById('progressChart');
            if (!canvas) return;

            try {
                // Get analytics data for chart
                const analyticsData = await API.getAnalytics({
                    period: '30d',
                    type: 'optimization_progress'
                });

                const ctx = canvas.getContext('2d');
                this.charts.progressChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: analyticsData.labels || this.generateDateLabels(30),
                        datasets: [{
                            label: 'Products Optimized',
                            data: analyticsData.optimized_count || this.generateSampleData(30, 0, 20),
                            borderColor: '#3B82F6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }, {
                            label: 'SEO Score Improvement',
                            data: analyticsData.score_improvement || this.generateSampleData(30, 0, 10),
                            borderColor: '#10B981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4,
                            yAxisID: 'y1'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Products Optimized'
                                }
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Score Improvement (%)'
                                },
                                grid: {
                                    drawOnChartArea: false,
                                }
                            }
                        }
                    }
                });

            } catch (error) {
                console.error('Error initializing progress chart:', error);
                
                // Show fallback chart with sample data
                const ctx = canvas.getContext('2d');
                this.charts.progressChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: this.generateDateLabels(30),
                        datasets: [{
                            label: 'Products Optimized',
                            data: this.generateSampleData(30, 0, 20),
                            borderColor: '#3B82F6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
        },

        /**
         * Initialize job status chart
         */
        async initializeJobStatusChart() {
            const canvas = document.getElementById('jobStatusChart');
            if (!canvas) return;

            try {
                // Get job status data
                const jobStats = await API.getStats({ type: 'job_status' });

                const ctx = canvas.getContext('2d');
                this.charts.jobStatusChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Completed', 'Running', 'Pending', 'Failed'],
                        datasets: [{
                            data: [
                                jobStats.completed || 0,
                                jobStats.running || 0,
                                jobStats.pending || 0,
                                jobStats.failed || 0
                            ],
                            backgroundColor: [
                                '#10B981', // Green for completed
                                '#3B82F6', // Blue for running
                                '#F59E0B', // Yellow for pending
                                '#EF4444'  // Red for failed
                            ],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right',
                            }
                        }
                    }
                });

            } catch (error) {
                console.error('Error initializing job status chart:', error);
                
                // Show fallback chart with sample data
                const ctx = canvas.getContext('2d');
                this.charts.jobStatusChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Completed', 'Running', 'Pending', 'Failed'],
                        datasets: [{
                            data: [45, 3, 2, 1],
                            backgroundColor: ['#10B981', '#3B82F6', '#F59E0B', '#EF4444'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right',
                            }
                        }
                    }
                });
            }
        },

        /**
         * Generate date labels for charts
         */
        generateDateLabels(days) {
            const labels = [];
            for (let i = days - 1; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
            }
            return labels;
        },

        /**
         * Generate sample data for charts
         */
        generateSampleData(count, min, max) {
            return Array.from({ length: count }, () => 
                Math.floor(Math.random() * (max - min + 1)) + min
            );
        },

        /**
         * Setup auto-refresh for dashboard data
         */
        setupAutoRefresh() {
            // Refresh dashboard data every 5 minutes
            setInterval(() => {
                this.loadDashboardData();
            }, 5 * 60 * 1000);

            // Refresh charts every 10 minutes
            setInterval(() => {
                this.refreshCharts();
            }, 10 * 60 * 1000);
        },

        /**
         * Refresh chart data
         */
        async refreshCharts() {
            if (this.activeTab === 'dashboard') {
                // Only refresh if dashboard tab is active
                this.initializeProgressChart();
                this.initializeJobStatusChart();
            }
        },

        /**
         * Handle WebSocket updates
         */
        handleWebSocketUpdate(data) {
            switch (data.type) {
                case 'stats_updated':
                    this.stats = { ...this.stats, ...data.stats };
                    break;
                
                case 'new_activity':
                    this.recentActivity.unshift(data.activity);
                    if (this.recentActivity.length > 10) {
                        this.recentActivity = this.recentActivity.slice(0, 10);
                    }
                    break;
                
                case 'job_completed':
                    this.stats.activeJobs = Math.max(0, this.stats.activeJobs - 1);
                    this.refreshCharts();
                    break;
                
                case 'job_started':
                    this.stats.activeJobs += 1;
                    break;
            }
        },

        /**
         * Add notification
         */
        addNotification(notification) {
            const notificationData = {
                id: Utils.generateUUID(),
                message: notification.message,
                type: notification.type || 'info',
                timestamp: new Date().toISOString(),
                ...notification
            };

            this.notifications.unshift(notificationData);
            
            // Limit notifications to 50
            if (this.notifications.length > 50) {
                this.notifications = this.notifications.slice(0, 50);
            }

            // Show toast notification
            Utils.showToast(notification.message, notification.type || 'info');
        },

        /**
         * Remove notification
         */
        removeNotification(notificationId) {
            this.notifications = this.notifications.filter(n => n.id !== notificationId);
        },

        /**
         * Format time for display
         */
        formatTime(timestamp) {
            return Utils.formatRelativeTime(timestamp);
        },

        /**
         * Load products (called when products tab is activated)
         */
        async loadProducts() {
            if (this.activeTab === 'products') {
                if (window.Products) {
                    await window.Products.init();
                }
            }
        },

        /**
         * Load jobs (called when jobs tab is activated)
         */
        async loadJobs() {
            if (this.activeTab === 'jobs') {
                if (window.Jobs) {
                    await window.Jobs.init();
                }
            }
        },

        /**
         * Save settings
         */
        async saveSettings() {
            try {
                await API.updateSettings(this.settings);
                Utils.showToast('Settings saved successfully', 'success');
            } catch (error) {
                console.error('Error saving settings:', error);
                Utils.showToast('Failed to save settings', 'error');
            }
        },

        /**
         * Logout user
         */
        async logout() {
            try {
                await Auth.logout();
            } catch (error) {
                // Logout locally even if API call fails
                Auth.clearAuth();
                window.location.href = 'login.html';
            }
        },

        /**
         * Watch for tab changes
         */
        $watch: {
            activeTab(newTab, oldTab) {
                // Initialize tab-specific modules
                this.$nextTick(() => {
                    switch (newTab) {
                        case 'products':
                            this.loadProducts();
                            break;
                        case 'jobs':
                            this.loadJobs();
                            break;
                        case 'dashboard':
                            // Refresh charts when returning to dashboard
                            if (oldTab !== 'dashboard') {
                                setTimeout(() => {
                                    this.refreshCharts();
                                }, 100);
                            }
                            break;
                    }
                });
            }
        }
    };
}

// Make dashboard app available globally
window.dashboardApp = dashboardApp;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Dashboard will be initialized by Alpine.js x-init
});
