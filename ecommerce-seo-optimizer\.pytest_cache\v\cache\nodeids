["test_api_comprehensive.py::TestGridSpokeFeatures::test_optimization_recommendations", "test_api_comprehensive.py::TestGridSpokeFeatures::test_seo_keyword_extraction", "test_api_comprehensive.py::TestGridSpokeFeatures::test_seo_score_calculation", "test_api_comprehensive.py::test_api_with_mock_optimization", "test_api_comprehensive.py::test_fastapi_creation", "test_api_comprehensive.py::test_mock_database_operations", "test_api_comprehensive.py::test_pydantic_product_model", "test_basic_functionality.py::TestBasicFunctionality::test_async_basic", "test_basic_functionality.py::TestBasicFunctionality::test_class_instantiation", "test_basic_functionality.py::test_fastapi_app_creation", "test_basic_functionality.py::test_imports_work", "test_basic_functionality.py::test_json_operations", "test_basic_functionality.py::test_pydantic_models", "test_basic_functionality.py::test_python_basics", "test_websocket_comprehensive.py::TestGridSpokeWebSocketFeatures::test_optimization_task_websocket_integration", "test_websocket_comprehensive.py::TestGridSpokeWebSocketFeatures::test_real_time_analytics_websocket", "test_websocket_comprehensive.py::test_optimization_progress_websocket", "test_websocket_comprehensive.py::test_websocket_basic_functionality", "test_websocket_comprehensive.py::test_websocket_broadcasting", "test_websocket_comprehensive.py::test_websocket_error_handling", "tests/database/test_migrations.py::TestDataIntegrity::test_data_type_migrations", "tests/database/test_migrations.py::TestDataIntegrity::test_database_backup_and_restore", "tests/database/test_migrations.py::TestDataIntegrity::test_json_field_migrations", "tests/database/test_migrations.py::TestDatabaseConstraints::test_cascade_deletes", "tests/database/test_migrations.py::TestDatabaseConstraints::test_foreign_key_constraints", "tests/database/test_migrations.py::TestDatabaseConstraints::test_not_null_constraints", "tests/database/test_migrations.py::TestDatabaseConstraints::test_unique_constraints", "tests/database/test_migrations.py::TestDatabaseIndexes::test_index_creation", "tests/database/test_migrations.py::TestDatabaseIndexes::test_query_performance_with_indexes", "tests/database/test_migrations.py::TestDatabaseMigrations::test_initial_migration_creates_tables", "tests/database/test_migrations.py::TestDatabaseMigrations::test_migration_data_preservation", "tests/database/test_migrations.py::TestDatabaseMigrations::test_migration_rollback_functionality", "tests/database/test_migrations.py::TestDatabaseMigrations::test_migration_version_consistency", "tests/database/test_migrations.py::TestDatabaseSecurity::test_database_connection_security", "tests/database/test_migrations.py::TestDatabaseSecurity::test_sensitive_data_encryption", "tests/database/test_migrations.py::TestDatabaseSecurity::test_sql_injection_protection", "tests/database/test_migrations.py::TestMigrationPerformance::test_large_table_migration_performance", "tests/database/test_migrations.py::TestMigrationPerformance::test_migration_rollback_performance", "tests/unit/test_agents.py::TestAgentIntegration::test_agent_error_propagation", "tests/unit/test_agents.py::TestAgentIntegration::test_agent_workflow_integration", "tests/unit/test_agents.py::TestContentGeneratorAgent::test_generate_blog_post", "tests/unit/test_agents.py::TestContentGeneratorAgent::test_generate_category_description", "tests/unit/test_agents.py::TestContentGeneratorAgent::test_generate_product_comparison", "tests/unit/test_agents.py::TestContentGeneratorAgent::test_generate_product_faqs", "tests/unit/test_agents.py::TestImageAnalyzerAgent::test_generate_alt_text", "tests/unit/test_agents.py::TestProductOptimizerAgent::test_agent_error_handling", "tests/unit/test_agents.py::TestProductOptimizerAgent::test_generate_keywords", "tests/unit/test_agents.py::TestProductOptimizerAgent::test_multi_model_support[anthropic/claude-3-opus-anthropic]", "tests/unit/test_agents.py::TestProductOptimizerAgent::test_multi_model_support[meta-llama/llama-2-70b-chat-meta-llama]", "tests/unit/test_agents.py::TestProductOptimizerAgent::test_multi_model_support[openai/gpt-4-openai]", "tests/unit/test_agents.py::TestProductOptimizerAgent::test_optimize_full_product", "tests/unit/test_agents.py::TestProductOptimizerAgent::test_optimize_meta_tags", "tests/unit/test_agents.py::TestProductOptimizerAgent::test_optimize_product_description_success", "tests/unit/test_agents.py::TestProductOptimizerAgent::test_optimize_product_title_success", "tests/unit/test_agents.py::TestSEOAnalyzerAgent::test_analyze_content_seo", "tests/unit/test_agents.py::TestSEOAnalyzerAgent::test_competitor_analysis", "tests/unit/test_agents.py::TestSEOAnalyzerAgent::test_keyword_opportunity_analysis", "tests/unit/test_agents.py::test_openrouter_api_mock_integration", "tests/unit/test_api.py::TestAnalyticsEndpoints::test_get_cost_analytics", "tests/unit/test_api.py::TestAnalyticsEndpoints::test_get_optimization_stats", "tests/unit/test_api.py::TestAnalyticsEndpoints::test_get_performance_metrics", "tests/unit/test_api.py::TestAuthenticationEndpoints::test_login_invalid_credentials", "tests/unit/test_api.py::TestAuthenticationEndpoints::test_login_success", "tests/unit/test_api.py::TestAuthenticationEndpoints::test_protected_endpoint_requires_auth", "tests/unit/test_api.py::TestAuthenticationEndpoints::test_token_refresh", "tests/unit/test_api.py::TestHealthCheckEndpoints::test_detailed_health_check", "tests/unit/test_api.py::TestHealthCheckEndpoints::test_health_check_endpoint", "tests/unit/test_api.py::TestProductManagementEndpoints::test_get_product_by_id", "tests/unit/test_api.py::TestProductManagementEndpoints::test_get_products_list", "tests/unit/test_api.py::TestProductManagementEndpoints::test_get_products_with_filters", "tests/unit/test_api.py::TestProductManagementEndpoints::test_update_product", "tests/unit/test_api.py::TestProductOptimizationEndpoints::test_bulk_optimization_job_creation", "tests/unit/test_api.py::TestProductOptimizationEndpoints::test_bulk_optimization_validation[invalid_data0-store_id]", "tests/unit/test_api.py::TestProductOptimizationEndpoints::test_bulk_optimization_validation[invalid_data1-store_id]", "tests/unit/test_api.py::TestProductOptimizationEndpoints::test_bulk_optimization_validation[invalid_data2-product_ids]", "tests/unit/test_api.py::TestProductOptimizationEndpoints::test_bulk_optimization_validation[invalid_data3-optimization_type]", "tests/unit/test_api.py::TestProductOptimizationEndpoints::test_cancel_optimization_job", "tests/unit/test_api.py::TestProductOptimizationEndpoints::test_get_optimization_job_not_found", "tests/unit/test_api.py::TestProductOptimizationEndpoints::test_get_optimization_job_status", "tests/unit/test_api.py::TestProductOptimizationEndpoints::test_optimize_product_auth_required", "tests/unit/test_api.py::TestProductOptimizationEndpoints::test_optimize_product_invalid_data", "tests/unit/test_api.py::TestProductOptimizationEndpoints::test_optimize_single_product_success", "tests/unit/test_api.py::TestSecurityEndpoints::test_rate_limiting", "tests/unit/test_api.py::TestSecurityEndpoints::test_sql_injection_protection", "tests/unit/test_api.py::TestSecurityEndpoints::test_xss_protection", "tests/unit/test_api.py::TestStoreManagementEndpoints::test_create_store_success", "tests/unit/test_api.py::TestStoreManagementEndpoints::test_create_store_validation_error", "tests/unit/test_api.py::TestStoreManagementEndpoints::test_delete_store", "tests/unit/test_api.py::TestStoreManagementEndpoints::test_get_store_by_id", "tests/unit/test_api.py::TestStoreManagementEndpoints::test_get_store_not_found", "tests/unit/test_api.py::TestStoreManagementEndpoints::test_get_stores_list", "tests/unit/test_api.py::TestStoreManagementEndpoints::test_update_store", "tests/unit/test_api.py::TestWebhookEndpoints::test_wordpress_webhook_invalid_signature", "tests/unit/test_api.py::TestWebhookEndpoints::test_wordpress_webhook_product_update"]