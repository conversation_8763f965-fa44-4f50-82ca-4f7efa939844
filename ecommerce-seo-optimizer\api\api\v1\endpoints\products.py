"""
Product management endpoints.
"""
import uuid
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from core.database import get_db_session
from core.security import get_current_user
from models.user import User as UserModel
from models.product import Product as ProductModel
from models.store import Store as StoreModel
from schemas.product import ProductCreate, Product, ProductUpdate
import structlog

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.get("/", response_model=list[Product])
async def get_products(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    store_id: uuid.UUID = Query(...),
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    """Get products for a store owned by current user."""
    # Validate store ownership
    store_res = await db.execute(select(StoreModel).where(StoreModel.id == store_id))
    store = store_res.scalar_one_or_none()
    if not store or store.owner_id != current_user.id:
        raise HTTPException(status_code=404, detail="Store not found")
    stmt = (
        select(ProductModel)
        .where(ProductModel.store_id == store_id)
        .offset(skip)
        .limit(limit)
    )
    result = await db.execute(stmt)
    products = result.scalars().all()
    return products


@router.get("/{product_id}", response_model=Product)
async def get_product(
    product_id: uuid.UUID,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    stmt = select(ProductModel).where(ProductModel.id == product_id)
    result = await db.execute(stmt)
    product = result.scalar_one_or_none()
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")
    # Verify ownership through store
    store_res = await db.execute(select(StoreModel).where(StoreModel.id == product.store_id))
    store = store_res.scalar_one_or_none()
    if not store or store.owner_id != current_user.id:
        raise HTTPException(status_code=404, detail="Product not found")
    return product

@router.post("/", response_model=Product, status_code=status.HTTP_201_CREATED)
async def create_product(
    product_in: ProductCreate,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    # Ownership check
    store_res = await db.execute(select(StoreModel).where(StoreModel.id == product_in.store_id))
    store = store_res.scalar_one_or_none()
    if not store or store.owner_id != current_user.id:
        raise HTTPException(status_code=404, detail="Store not found")
    # Prevent duplicate external_id per store
    dup_res = await db.execute(
        select(ProductModel).where(
            ProductModel.store_id == product_in.store_id,
            ProductModel.external_id == product_in.external_id
        )
    )
    if dup_res.scalar_one_or_none():
        raise HTTPException(status_code=400, detail="Product external_id already exists in store")
    db_obj = ProductModel(
        store_id=product_in.store_id,
        external_id=product_in.external_id,
        title=product_in.name,
        sku=product_in.sku,
        description=product_in.description,
        price=product_in.price,
        categories=product_in.categories or [],
        tags=product_in.tags or [],
        image_urls=[str(u) for u in (product_in.images or [])],
        attributes=product_in.original_data or {},
    )
    db.add(db_obj)
    await db.commit()
    await db.refresh(db_obj)
    logger.info("Product created", product_id=str(db_obj.id), store_id=str(store.id))
    # Update store product count (simple increment)
    store.total_products += 1
    await db.commit()
    return db_obj


@router.post("/{product_id}/optimize")
async def optimize_product(
    product_id: uuid.UUID,
    db: AsyncSession = Depends(get_db_session),
    current_user: UserModel = Depends(get_current_user)
) -> Any:
    # Placeholder: confirm ownership and respond. Actual optimization logic will be queued via Celery in later phase.
    stmt = select(ProductModel).where(ProductModel.id == product_id)
    result = await db.execute(stmt)
    product = result.scalar_one_or_none()
    if not product:
        raise HTTPException(status_code=404, detail="Product not found")
    store_res = await db.execute(select(StoreModel).where(StoreModel.id == product.store_id))
    store = store_res.scalar_one_or_none()
    if not store or store.owner_id != current_user.id:
        raise HTTPException(status_code=404, detail="Product not found")
    return {"message": f"Optimization queued for product {product_id}", "status": "pending"}
