/**
 * GridSpoke SEO Optimizer - Dashboard Main Module - NEW VERSION
 * Handles dashboard state, chart initialization, and tab management
 * CACHE BUSTER: 2024-01-01-16:00 - BRAND NEW FILE
 */

function dashboardApp() {
    return {
        // Dashboard state
        activeTab: 'dashboard',
        showNotifications: false,
        
        // User data
        user: {
            name: '',
            email: '',
            avatar: ''
        },

        // Dashboard statistics
        stats: {
            totalProducts: 0,
            optimizedProducts: 0,
            activeJobs: 0,
            averageSeoScore: 0
        },

        // Recent activity
        recentActivity: [],

        // Notifications
        notifications: [],

        // Settings
        settings: {
            apiBaseUrl: 'http://localhost:8000/api/v1',
            websocketUrl: 'ws://localhost:8000/ws',
            autoOptimize: false,
            emailNotifications: true
        },

        // Charts
        charts: {
            progressChart: null,
            jobStatusChart: null
        },

        /**
         * Initialize dashboard - ENHANCED WITH DEBUGGING
         */
        async init() {
            const initId = Date.now() + '_' + Math.random();
            console.log(`🚀 NEW DASHBOARD INIT CALLED [${initId}] - Entry point`);
            console.log(`📍 Current timestamp: ${new Date().toISOString()}`);
            
            // Multiple safeguards to prevent repeated initialization
            if (window.dashboardInitialized || window.dashboardInitializing) {
                console.log(`⚠️ Dashboard already initialized/initializing [${initId}], skipping...`);
                return;
            }
            
            // Set initializing flag immediately
            window.dashboardInitializing = true;
            
            console.log(`🔄 Starting dashboard initialization [${initId}]...`);
            
            // Add try-catch to capture any errors
            try {
                // Immediate auth check with detailed logging
                console.log(`🔐 Checking authentication [${initId}]...`);
                
                // Check if Auth module exists
                if (typeof Auth === 'undefined') {
                    console.error(`❌ Auth module not available [${initId}]`);
                    console.log(`🔄 Waiting for Auth module to load [${initId}]...`);
                    
                    // Wait for Auth module to be available
                    let retryCount = 0;
                    const maxRetries = 10;
                    const checkAuthInterval = setInterval(() => {
                        retryCount++;
                        console.log(`🔄 Retry ${retryCount}/${maxRetries} checking for Auth module [${initId}]...`);
                        
                        if (typeof Auth !== 'undefined') {
                            console.log(`✅ Auth module now available [${initId}]`);
                            clearInterval(checkAuthInterval);
                            // Clear the initializing flag and retry
                            window.dashboardInitializing = false;
                            this.init();
                            return;
                        }
                        
                        if (retryCount >= maxRetries) {
                            console.error(`🚨 Auth module still not available after ${maxRetries} retries [${initId}]`);
                            clearInterval(checkAuthInterval);
                            window.dashboardInitializing = false;
                            throw new Error('Auth module not loaded');
                        }
                    }, 100); // Check every 100ms
                    
                    return;
                }
                
                console.log(`✅ Auth module available [${initId}]`);
                console.log(`📊 Auth state [${initId}]:`, Auth.state);
                
                // Check authentication with detailed debugging
                const isAuth = Auth.isAuthenticated();
                console.log(`🔍 Auth check result [${initId}]:`, isAuth);
                
                // Get token details for debugging
                const token = Utils.storage.get(Auth.config.tokenStorageKey);
                const user = Utils.storage.get(Auth.config.userStorageKey);
                console.log(`🔑 Raw token from storage [${initId}]:`, token);
                console.log(`👤 Raw user from storage [${initId}]:`, user);
                console.log(`✅ Token valid check [${initId}]:`, Auth.isTokenValid(token));
                
                if (!isAuth) {
                    console.log(`🚪 User not authenticated [${initId}], redirecting to login...`);
                    console.log(`🔍 Auth state when not authenticated [${initId}]:`, Auth.state);
                    
                    // Set a flag to prevent multiple redirects
                    if (!sessionStorage.getItem('redirecting_to_login')) {
                        console.log(`🔄 Setting redirect flag [${initId}]`);
                        sessionStorage.setItem('redirecting_to_login', 'true');
                        console.log(`🔄 About to redirect [${initId}]`);
                        window.location.href = 'login.html';
                    } else {
                        console.log(`⚠️ Already redirecting to login [${initId}]`);
                    }
                    return;
                }
                
                console.log(`✅ User authenticated [${initId}], proceeding with dashboard...`);
                
                // Clear any redirect flag since we're authenticated
                sessionStorage.removeItem('redirecting_to_login');
                
                // Mark as fully initialized
                window.dashboardInitialized = true;
                window.dashboardInitializing = false;
                
                // Load user data
                console.log(`📊 Loading user data [${initId}]...`);
                await this.loadUserData();
                
                // Load dashboard data
                console.log(`📊 Loading dashboard data [${initId}]...`);
                await this.loadDashboardData();
                
                // Initialize WebSocket connection
                console.log(`🔌 Initializing WebSocket [${initId}]...`);
                this.initializeWebSocket();
                
                // Initialize charts after DOM is ready
                console.log(`📈 Initializing charts [${initId}]...`);
                this.$nextTick(() => {
                    this.initializeCharts();
                });

                // Setup auto-refresh
                console.log(`🔄 Setting up auto-refresh [${initId}]...`);
                this.setupAutoRefresh();
                
                console.log(`✅ Dashboard initialization complete [${initId}]`);
                
            } catch (error) {
                console.error(`🚨 ERROR in dashboard initialization [${initId}]:`, error);
                console.error(`🚨 Error stack [${initId}]:`, error.stack);
                window.dashboardInitializing = false;
                
                // On error, also redirect to login
                if (!sessionStorage.getItem('redirecting_to_login')) {
                    console.log(`🚨 Error occurred, redirecting to login [${initId}]`);
                    sessionStorage.setItem('redirecting_to_login', 'true');
                    window.location.href = 'login.html';
                }
            }
        },

        /**
         * Load user data
         */
        async loadUserData() {
            try {
                const user = Auth.getUser();
                if (user) {
                    this.user = user;
                }
            } catch (error) {
                console.error('Error loading user data:', error);
            }
        },

        /**
         * Load dashboard data from API
         */
        async loadDashboardData() {
            try {
                // Load dashboard statistics
                const response = await fetch(`${this.settings.apiBaseUrl}/dashboard/stats`, {
                    headers: {
                        'Authorization': `Bearer ${Auth.getToken()}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    this.stats = data;
                } else {
                    console.error('Failed to load dashboard stats');
                }

                // Load recent activity
                await this.loadRecentActivity();
                
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        },

        /**
         * Initialize WebSocket connection
         */
        initializeWebSocket() {
            try {
                if (typeof WebSocketManager !== 'undefined') {
                    WebSocketManager.init(this.settings.websocketUrl);
                    
                    // Subscribe to dashboard updates
                    WebSocketManager.subscribe('dashboard_update', (data) => {
                        this.handleDashboardUpdate(data);
                    });
                }
            } catch (error) {
                console.error('Error initializing WebSocket:', error);
            }
        },

        /**
         * Initialize charts
         */
        initializeCharts() {
            this.initializeProgressChart();
            this.initializeJobStatusChart();
        },

        /**
         * Initialize progress chart
         */
        async initializeProgressChart() {
            // ... rest of chart initialization
        },

        /**
         * Initialize job status chart  
         */
        async initializeJobStatusChart() {
            // ... rest of chart initialization
        },

        /**
         * Load recent activity
         */
        async loadRecentActivity() {
            // ... rest of activity loading
        },

        /**
         * Setup auto-refresh
         */
        setupAutoRefresh() {
            // ... rest of auto-refresh setup
        },

        /**
         * Handle dashboard updates from WebSocket
         */
        handleDashboardUpdate(data) {
            // ... rest of update handling
        }
    };
}
