<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GridSpoke SEO Optimizer - Dashboard</title>
    
    <!-- Tailwind CSS v3 CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js v3 CDN -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="css/styles.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'gridspoke-primary': '#3B82F6',
                        'gridspoke-secondary': '#8B5CF6',
                        'gridspoke-success': '#10B981',
                        'gridspoke-warning': '#F59E0B',
                        'gridspoke-error': '#EF4444',
                        'gridspoke-dark': '#1F2937',
                    }
                }
            }
        }
    </script>
    <script>
        // Initialize Auth before Alpine.js starts
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure Auth module is initialized
            if (typeof Auth !== 'undefined' && Auth.init) {
                Auth.init();
            }
        });
    </script>
</head>
<body class="bg-gray-50" x-data="dashboardApp()" x-init="init()">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Logo and Navigation -->
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <div class="h-8 w-8 bg-gridspoke-primary rounded-lg flex items-center justify-center">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <span class="ml-2 text-xl font-bold text-gray-900">GridSpoke</span>
                    </div>
                    
                    <!-- Desktop Navigation -->
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <button @click="activeTab = 'dashboard'" 
                                :class="activeTab === 'dashboard' ? 'border-gridspoke-primary text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Dashboard
                        </button>
                        <button @click="activeTab = 'products'" 
                                :class="activeTab === 'products' ? 'border-gridspoke-primary text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Products
                        </button>
                        <button @click="activeTab = 'jobs'" 
                                :class="activeTab === 'jobs' ? 'border-gridspoke-primary text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Jobs
                        </button>
                        <button @click="activeTab = 'settings'" 
                                :class="activeTab === 'settings' ? 'border-gridspoke-primary text-gray-900' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                class="inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Settings
                        </button>
                    </div>
                </div>
                
                <!-- Right side -->
                <div class="flex items-center space-x-4">
                    <!-- Notifications -->
                    <button @click="showNotifications = !showNotifications" class="relative p-2 text-gray-400 hover:text-gray-500">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5-5V9.09c0-2.83-2.31-5.14-5.14-5.14S4.72 6.26 4.72 9.09V12l-5 5h5m10 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                        </svg>
                        <span x-show="notifications.length > 0" x-text="notifications.length" class="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"></span>
                    </button>
                    
                    <!-- User Menu -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="flex items-center space-x-2 text-sm text-gray-700 hover:text-gray-900">
                            <div class="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center">
                                <span x-text="user.name ? user.name.charAt(0).toUpperCase() : 'U'" class="text-gray-600 font-medium"></span>
                            </div>
                            <span x-text="user.name || 'User'"></span>
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        
                        <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5">
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Billing</a>
                            <button @click="logout()" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Dashboard Tab -->
        <div x-show="activeTab === 'dashboard'">
            <!-- Stats Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-blue-500 rounded-md flex items-center justify-center">
                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Products</dt>
                                <dd class="text-lg font-medium text-gray-900" x-text="stats.totalProducts"></dd>
                            </dl>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-green-500 rounded-md flex items-center justify-center">
                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Optimized</dt>
                                <dd class="text-lg font-medium text-gray-900" x-text="stats.optimizedProducts"></dd>
                            </dl>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-yellow-500 rounded-md flex items-center justify-center">
                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Active Jobs</dt>
                                <dd class="text-lg font-medium text-gray-900" x-text="stats.activeJobs"></dd>
                            </dl>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 bg-purple-500 rounded-md flex items-center justify-center">
                                <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">SEO Score</dt>
                                <dd class="text-lg font-medium text-gray-900" x-text="stats.averageSeoScore + '%'"></dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts Row -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- Optimization Progress Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Optimization Progress</h3>
                    <canvas id="progressChart" width="400" height="200"></canvas>
                </div>
                
                <!-- Job Status Chart -->
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Job Status Distribution</h3>
                    <canvas id="jobStatusChart" width="400" height="200"></canvas>
                </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Recent Activity</h3>
                </div>
                <div class="p-6">
                    <div class="flow-root">
                        <ul class="-mb-8">
                            <template x-for="(activity, index) in recentActivity" :key="index">
                                <li>
                                    <div class="relative pb-8" x-show="index < recentActivity.length - 1">
                                        <span class="absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200"></span>
                                    </div>
                                    <div class="relative flex items-start space-x-3">
                                        <div>
                                            <div class="relative px-1">
                                                <div class="h-8 w-8 bg-gray-100 rounded-full ring-8 ring-white flex items-center justify-center">
                                                    <svg class="h-5 w-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="min-w-0 flex-1">
                                            <div>
                                                <div class="text-sm">
                                                    <span class="font-medium text-gray-900" x-text="activity.message"></span>
                                                </div>
                                                <p class="mt-0.5 text-sm text-gray-500" x-text="formatTime(activity.timestamp)"></p>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </template>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Products Tab -->
        <div x-show="activeTab === 'products'">
            <div id="products-container">
                <!-- Products content will be loaded here -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">Products</h3>
                            <button @click="loadProducts()" class="bg-gridspoke-primary text-white px-4 py-2 rounded-md hover:bg-blue-600">
                                Refresh
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-500">Loading products...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Jobs Tab -->
        <div x-show="activeTab === 'jobs'">
            <div id="jobs-container">
                <!-- Jobs content will be loaded here -->
                <div class="bg-white rounded-lg shadow">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">Optimization Jobs</h3>
                            <button @click="loadJobs()" class="bg-gridspoke-primary text-white px-4 py-2 rounded-md hover:bg-blue-600">
                                Refresh
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-500">Loading jobs...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div x-show="activeTab === 'settings'">
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Settings</h3>
                </div>
                <div class="p-6">
                    <div class="space-y-6">
                        <!-- API Configuration -->
                        <div>
                            <h4 class="text-base font-medium text-gray-900 mb-4">API Configuration</h4>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">API Base URL</label>
                                    <input type="url" x-model="settings.apiBaseUrl" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-gridspoke-primary focus:border-gridspoke-primary">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">WebSocket URL</label>
                                    <input type="url" x-model="settings.websocketUrl" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-gridspoke-primary focus:border-gridspoke-primary">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Optimization Settings -->
                        <div>
                            <h4 class="text-base font-medium text-gray-900 mb-4">Optimization Settings</h4>
                            <div class="space-y-4">
                                <div class="flex items-center">
                                    <input type="checkbox" x-model="settings.autoOptimize" class="h-4 w-4 text-gridspoke-primary focus:ring-gridspoke-primary border-gray-300 rounded">
                                    <label class="ml-2 block text-sm text-gray-700">Auto-optimize new products</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" x-model="settings.emailNotifications" class="h-4 w-4 text-gridspoke-primary focus:ring-gridspoke-primary border-gray-300 rounded">
                                    <label class="ml-2 block text-sm text-gray-700">Email notifications</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="pt-4">
                            <button @click="saveSettings()" class="bg-gridspoke-primary text-white px-4 py-2 rounded-md hover:bg-blue-600">
                                Save Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Panel -->
    <div x-show="showNotifications" x-transition class="fixed inset-0 z-50">
        <div class="absolute inset-0 bg-black bg-opacity-25" @click="showNotifications = false"></div>
        <div class="absolute top-16 right-4 w-80 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5">
            <div class="p-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Notifications</h3>
            </div>
            <div class="max-h-96 overflow-y-auto">
                <template x-for="notification in notifications" :key="notification.id">
                    <div class="p-4 border-b border-gray-100 hover:bg-gray-50">
                        <div class="flex justify-between">
                            <p class="text-sm text-gray-900" x-text="notification.message"></p>
                            <button @click="removeNotification(notification.id)" class="text-gray-400 hover:text-gray-600">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                        <p class="text-xs text-gray-500 mt-1" x-text="formatTime(notification.timestamp)"></p>
                    </div>
                </template>
                <div x-show="notifications.length === 0" class="p-4 text-center text-gray-500">
                    No notifications
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/utils.js"></script>
    <script src="js/auth.js?v=20241221-1235"></script>
    <script src="js/api.js"></script>
    <script src="js/websocket.js"></script>
    <script src="js/products.js"></script>
    <script src="js/jobs.js"></script>
    <script src="js/dashboard-new.js?v=20241221-1234"></script>
</body>
</html>
