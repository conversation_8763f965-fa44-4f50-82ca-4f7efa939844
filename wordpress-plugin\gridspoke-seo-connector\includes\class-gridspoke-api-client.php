<?php
/**
 * GridSpoke API Client
 * 
 * Handles all communication with the GridSpoke backend service
 * for product optimization requests and status updates.
 */

if (!defined('ABSPATH')) {
    exit;
}

class GridSpoke_API_Client {
    
    /**
     * Singleton instance
     */
    private static $instance = null;
    
    /**
     * API settings
     */
    private $api_endpoint;
    private $api_key;
    private $timeout = 30;
    private $access_token = null;
    private $refresh_token = null;
    private $store_id = null;
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $settings = GridSpoke_SEO_Connector::get_settings();
        $this->api_endpoint = trailingslashit($settings['api_endpoint'] ?? 'http://localhost:8000/api/v1');
        $this->api_key = $settings['api_key'] ?? '';

        // Load stored tokens
        $this->access_token = get_transient('gridspoke_access_token');
        $this->refresh_token = get_option('gridspoke_refresh_token');
        $this->store_id = get_option('gridspoke_store_id');
    }

    /**
     * Authenticate with email/password and get JWT tokens
     */
    public function authenticate($email, $password) {
        $payload = array(
            'email' => $email,
            'password' => $password
        );

        $response = $this->make_request('POST', 'auth/login', $payload, false);

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $body = json_decode(wp_remote_retrieve_body($response), true);

        if ($status_code === 200 && !empty($body['access_token'])) {
            // Store tokens
            $this->access_token = $body['access_token'];
            $this->refresh_token = $body['refresh_token'];

            // Cache access token for 1 hour (it expires in 30 minutes by default)
            set_transient('gridspoke_access_token', $this->access_token, 3600);
            update_option('gridspoke_refresh_token', $this->refresh_token);

            // Store user info
            if (!empty($body['user'])) {
                update_option('gridspoke_user_info', $body['user']);
            }

            return array(
                'success' => true,
                'message' => __('Authentication successful', 'gridspoke-seo'),
                'user' => $body['user'] ?? null
            );
        } else {
            return array(
                'success' => false,
                'message' => $body['detail'] ?? __('Authentication failed', 'gridspoke-seo')
            );
        }
    }

    /**
     * Refresh access token using refresh token
     */
    public function refresh_access_token() {
        if (empty($this->refresh_token)) {
            return false;
        }

        $payload = array(
            'refresh_token' => $this->refresh_token
        );

        $response = $this->make_request('POST', 'auth/refresh', $payload, false);

        if (is_wp_error($response)) {
            return false;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $body = json_decode(wp_remote_retrieve_body($response), true);

        if ($status_code === 200 && !empty($body['access_token'])) {
            $this->access_token = $body['access_token'];
            $this->refresh_token = $body['refresh_token'];

            set_transient('gridspoke_access_token', $this->access_token, 3600);
            update_option('gridspoke_refresh_token', $this->refresh_token);

            return true;
        }

        return false;
    }

    /**
     * Ensure we have a valid access token
     */
    private function ensure_authenticated() {
        if (empty($this->access_token)) {
            // Try to refresh token
            if (!$this->refresh_access_token()) {
                return new WP_Error('not_authenticated', __('Not authenticated with GridSpoke API', 'gridspoke-seo'));
            }
        }
        return true;
    }

    /**
     * Test API connection
     */
    public function test_connection() {
        // First test basic health endpoint (no auth required)
        $response = $this->make_request('GET', 'health', null, false);

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }

        $status_code = wp_remote_retrieve_response_code($response);

        if ($status_code === 200) {
            // Test authenticated endpoint if we have credentials
            if (!empty($this->access_token) || !empty($this->refresh_token)) {
                $auth_test = $this->test_authenticated_connection();
                if (!$auth_test['success']) {
                    return array(
                        'success' => true,
                        'message' => __('Basic connection successful, but authentication failed. Please check your credentials.', 'gridspoke-seo'),
                        'auth_status' => 'failed'
                    );
                }

                return array(
                    'success' => true,
                    'message' => __('Connection and authentication successful', 'gridspoke-seo'),
                    'auth_status' => 'success'
                );
            }

            return array(
                'success' => true,
                'message' => __('Basic connection successful. Please authenticate to access full features.', 'gridspoke-seo'),
                'auth_status' => 'not_authenticated'
            );
        } else {
            return array(
                'success' => false,
                'message' => sprintf(__('API returned status code: %d', 'gridspoke-seo'), $status_code)
            );
        }
    }

    /**
     * Test authenticated connection
     */
    private function test_authenticated_connection() {
        $auth_check = $this->ensure_authenticated();
        if (is_wp_error($auth_check)) {
            return array(
                'success' => false,
                'message' => $auth_check->get_error_message()
            );
        }

        $response = $this->make_request('GET', 'auth/me');

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }

        $status_code = wp_remote_retrieve_response_code($response);

        return array(
            'success' => $status_code === 200,
            'message' => $status_code === 200 ?
                __('Authentication successful', 'gridspoke-seo') :
                sprintf(__('Authentication failed with status: %d', 'gridspoke-seo'), $status_code)
        );
    }
    
    /**
     * Sync products to GridSpoke backend
     */
    public function sync_products($products, $store_type = 'woocommerce') {
        // Ensure we have authentication
        $auth_check = $this->ensure_authenticated();
        if (is_wp_error($auth_check)) {
            GridSpoke_Logger::log('Product sync failed: ' . $auth_check->get_error_message(), 'error');
            return false;
        }

        // Ensure we have a store
        if (empty($this->store_id)) {
            $store_result = $this->ensure_store_exists($store_type);
            if (!$store_result) {
                GridSpoke_Logger::log('Product sync failed: Could not create or find store', 'error');
                return false;
            }
        }

        // Convert products to the expected format
        $formatted_products = array();
        foreach ($products as $product) {
            $formatted_products[] = array(
                'store_id' => $this->store_id,
                'external_id' => (string) $product['id'],
                'name' => $product['name'],
                'sku' => $product['sku'] ?? '',
                'description' => $product['description'] ?? '',
                'price' => !empty($product['price']) ? floatval($product['price']) : null,
                'categories' => $product['categories'] ?? array(),
                'tags' => $product['tags'] ?? array(),
                'images' => array_map(function($img) {
                    return $img['url'] ?? $img;
                }, $product['images'] ?? array()),
                'original_data' => $product
            );
        }

        $response = $this->make_request('POST', 'products/bulk/sync', $formatted_products);

        if (is_wp_error($response)) {
            GridSpoke_Logger::log('Product sync failed: ' . $response->get_error_message(), 'error');
            return false;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $body = json_decode(wp_remote_retrieve_body($response), true);

        if ($status_code === 200 || $status_code === 201) {
            GridSpoke_Logger::log(sprintf('Successfully synced %d products (created: %d, updated: %d)',
                count($products),
                $body['created'] ?? 0,
                $body['updated'] ?? 0), 'info');
            return $body;
        } else {
            GridSpoke_Logger::log(sprintf('Product sync failed with status %d: %s', $status_code, $body['detail'] ?? 'Unknown error'), 'error');
            return false;
        }
    }

    /**
     * Ensure store exists in GridSpoke backend
     */
    private function ensure_store_exists($store_type = 'woocommerce') {
        // Check if we already have a store ID
        if (!empty($this->store_id)) {
            return true;
        }

        // Get existing stores
        $response = $this->make_request('GET', 'stores');

        if (is_wp_error($response)) {
            return false;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $body = json_decode(wp_remote_retrieve_body($response), true);

        if ($status_code === 200 && !empty($body)) {
            // Look for existing store with matching domain
            $site_domain = parse_url(home_url(), PHP_URL_HOST);

            foreach ($body as $store) {
                if ($store['domain'] === $site_domain) {
                    $this->store_id = $store['id'];
                    update_option('gridspoke_store_id', $this->store_id);
                    return true;
                }
            }
        }

        // Create new store if none exists
        $store_data = array(
            'name' => get_bloginfo('name'),
            'store_url' => home_url(),
            'platform' => $store_type,
            'api_credentials' => array()
        );

        $response = $this->make_request('POST', 'stores', $store_data);

        if (is_wp_error($response)) {
            return false;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $body = json_decode(wp_remote_retrieve_body($response), true);

        if ($status_code === 201 && !empty($body['id'])) {
            $this->store_id = $body['id'];
            update_option('gridspoke_store_id', $this->store_id);
            GridSpoke_Logger::log('Created new store in GridSpoke: ' . $this->store_id, 'info');
            return true;
        }

        return false;
    }

    /**
     * Request optimization for specific products
     */
    public function request_optimization($product_ids, $options = array()) {
        // Ensure we have authentication
        $auth_check = $this->ensure_authenticated();
        if (is_wp_error($auth_check)) {
            GridSpoke_Logger::log('Optimization request failed: ' . $auth_check->get_error_message(), 'error');
            return false;
        }

        $default_options = array(
            'optimization_types' => array('title', 'description', 'meta_description'),
            'ai_model' => 'claude-3-sonnet',
            'target_keywords' => array(),
            'custom_instructions' => ''
        );

        $options = wp_parse_args($options, $default_options);

        // Convert WordPress product IDs to UUIDs (we'll need to map these)
        $uuid_product_ids = array();
        foreach ($product_ids as $wp_product_id) {
            $uuid = $this->get_product_uuid($wp_product_id);
            if ($uuid) {
                $uuid_product_ids[] = $uuid;
            }
        }

        if (empty($uuid_product_ids)) {
            GridSpoke_Logger::log('No valid product UUIDs found for optimization', 'error');
            return false;
        }

        // Use bulk optimization if multiple products
        if (count($uuid_product_ids) > 1) {
            $bulk_action = array(
                'product_ids' => $uuid_product_ids,
                'action' => 'optimize',
                'parameters' => $options
            );

            $optimization_request = array(
                'optimization_types' => $options['optimization_types'],
                'ai_model' => $options['ai_model'],
                'target_keywords' => $options['target_keywords'],
                'custom_instructions' => $options['custom_instructions']
            );

            $response = $this->make_request('POST', 'products/bulk/optimize', array(
                'bulk_action' => $bulk_action,
                'optimization_request' => $optimization_request
            ));
        } else {
            // Single product optimization
            $optimization_request = array(
                'optimization_types' => $options['optimization_types'],
                'ai_model' => $options['ai_model'],
                'target_keywords' => $options['target_keywords'],
                'custom_instructions' => $options['custom_instructions']
            );

            $response = $this->make_request('POST', 'products/' . $uuid_product_ids[0] . '/optimize', $optimization_request);
        }

        if (is_wp_error($response)) {
            GridSpoke_Logger::log('Optimization request failed: ' . $response->get_error_message(), 'error');
            return false;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $body = json_decode(wp_remote_retrieve_body($response), true);

        if ($status_code === 200 || $status_code === 202) {
            GridSpoke_Logger::log(sprintf('Optimization requested for %d products', count($product_ids)), 'info');
            return $body;
        } else {
            GridSpoke_Logger::log(sprintf('Optimization request failed with status %d: %s', $status_code, $body['detail'] ?? 'Unknown error'), 'error');
            return false;
        }
    }

    /**
     * Get product UUID from WordPress product ID
     */
    private function get_product_uuid($wp_product_id) {
        // For now, we'll store the mapping in WordPress meta
        $uuid = get_post_meta($wp_product_id, '_gridspoke_product_uuid', true);

        if (empty($uuid)) {
            // If no UUID exists, we need to sync this product first
            GridSpoke_Logger::log("Product $wp_product_id has no GridSpoke UUID. Sync required.", 'warning');
            return null;
        }

        return $uuid;
    }

    /**
     * Store product UUID mapping
     */
    public function store_product_uuid($wp_product_id, $uuid) {
        return update_post_meta($wp_product_id, '_gridspoke_product_uuid', $uuid);
    }

    /**
     * Get optimization status
     */
    public function get_optimization_status($optimization_id) {
        $response = $this->make_request('GET', 'optimizations/' . $optimization_id . '/status');
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        
        if ($status_code === 200) {
            return json_decode(wp_remote_retrieve_body($response), true);
        }
        
        return false;
    }
    
    /**
     * Get store analytics
     */
    public function get_analytics($period = '30d') {
        $response = $this->make_request('GET', 'analytics', array('period' => $period));
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        
        if ($status_code === 200) {
            return json_decode(wp_remote_retrieve_body($response), true);
        }
        
        return false;
    }
    
    /**
     * Register webhook with backend
     */
    public function register_webhook() {
        $settings = GridSpoke_SEO_Connector::get_settings();
        
        $payload = array(
            'webhook_url' => $this->get_webhook_url(),
            'webhook_secret' => $settings['webhook_secret'] ?? '',
            'events' => array('optimization.completed', 'optimization.failed', 'sync.completed')
        );
        
        $response = $this->make_request('POST', 'webhooks/register', $payload);
        
        if (is_wp_error($response)) {
            return false;
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        return $status_code === 200 || $status_code === 201;
    }
    
    /**
     * Make HTTP request to GridSpoke API
     */
    private function make_request($method, $endpoint, $data = null, $require_auth = true) {
        $url = $this->api_endpoint . ltrim($endpoint, '/');

        $headers = array(
            'Content-Type' => 'application/json',
            'User-Agent' => 'GridSpoke-WordPress-Plugin/' . GRIDSPOKE_SEO_VERSION,
            'X-WordPress-URL' => home_url(),
            'X-Plugin-Version' => GRIDSPOKE_SEO_VERSION
        );

        // Add authentication header if required and available
        if ($require_auth) {
            if (empty($this->access_token)) {
                return new WP_Error('no_access_token', __('Not authenticated with GridSpoke API', 'gridspoke-seo'));
            }
            $headers['Authorization'] = 'Bearer ' . $this->access_token;
        }

        $args = array(
            'method' => strtoupper($method),
            'timeout' => $this->timeout,
            'headers' => $headers,
            'sslverify' => true
        );
        
        if ($data !== null && in_array($method, array('POST', 'PUT', 'PATCH'))) {
            $args['body'] = wp_json_encode($data);
        } elseif ($data !== null && $method === 'GET') {
            $url = add_query_arg($data, $url);
        }
        
        // Log the request (without sensitive data)
        $log_data = array(
            'method' => $method,
            'endpoint' => $endpoint,
            'url' => $url
        );
        GridSpoke_Logger::log('API Request: ' . wp_json_encode($log_data), 'debug');
        
        $response = wp_remote_request($url, $args);
        
        // Log response status
        if (!is_wp_error($response)) {
            $status_code = wp_remote_retrieve_response_code($response);
            GridSpoke_Logger::log(sprintf('API Response: %d for %s %s', $status_code, $method, $endpoint), 'debug');
        }
        
        return $response;
    }
    
    /**
     * Get webhook URL for this site
     */
    private function get_webhook_url() {
        return rest_url('gridspoke-seo/v1/webhook');
    }
    
    /**
     * Validate API key format
     */
    public function validate_api_key($api_key) {
        // GridSpoke API keys should be 64 character hex strings
        if (strlen($api_key) !== 64 || !ctype_xdigit($api_key)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Update API credentials
     */
    public function update_credentials($api_endpoint, $api_key) {
        $this->api_endpoint = trailingslashit($api_endpoint);
        $this->api_key = $api_key;
    }
    
    /**
     * Get rate limit info from last response
     */
    public function get_rate_limit_info($response) {
        if (is_wp_error($response)) {
            return null;
        }
        
        $headers = wp_remote_retrieve_headers($response);
        
        return array(
            'limit' => $headers['X-RateLimit-Limit'] ?? null,
            'remaining' => $headers['X-RateLimit-Remaining'] ?? null,
            'reset' => $headers['X-RateLimit-Reset'] ?? null
        );
    }
    
    /**
     * Handle API errors gracefully
     */
    public function handle_api_error($response) {
        if (is_wp_error($response)) {
            return array(
                'error' => 'request_failed',
                'message' => $response->get_error_message()
            );
        }
        
        $status_code = wp_remote_retrieve_response_code($response);
        $body = json_decode(wp_remote_retrieve_body($response), true);
        
        $error_messages = array(
            400 => __('Bad request - invalid data sent', 'gridspoke-seo'),
            401 => __('Unauthorized - check your API key', 'gridspoke-seo'),
            403 => __('Forbidden - insufficient permissions', 'gridspoke-seo'),
            404 => __('Not found - invalid endpoint', 'gridspoke-seo'),
            429 => __('Rate limit exceeded - please try again later', 'gridspoke-seo'),
            500 => __('Server error - please try again later', 'gridspoke-seo'),
            503 => __('Service unavailable - maintenance mode', 'gridspoke-seo')
        );
        
        return array(
            'error' => 'api_error',
            'status_code' => $status_code,
            'message' => $body['message'] ?? $error_messages[$status_code] ?? __('Unknown error occurred', 'gridspoke-seo')
        );
    }
}
