<?php
/**
 * Admin functionality for GridSpoke SEO Connector
 * 
 * Handles all WordPress admin interface components including
 * settings pages, dashboard widgets, and admin notices.
 */

if (!defined('ABSPATH')) {
    exit;
}

class GridSpoke_Admin {
    
    /**
     * Singleton instance
     */
    private static $instance = null;
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize admin hooks
     */
    private function init_hooks() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        add_action('wp_dashboard_setup', array($this, 'add_dashboard_widget'));
        add_action('admin_init', array($this, 'handle_admin_actions'));
        
        // AJAX handlers
        add_action('wp_ajax_gridspoke_test_connection', array($this, 'ajax_test_connection'));
        add_action('wp_ajax_gridspoke_test_authentication', array($this, 'ajax_test_authentication'));
        add_action('wp_ajax_gridspoke_sync_all_products', array($this, 'ajax_sync_all_products'));
        add_action('wp_ajax_gridspoke_get_optimization_stats', array($this, 'ajax_get_optimization_stats'));
        add_action('wp_ajax_gridspoke_clear_logs', array($this, 'ajax_clear_logs'));
        add_action('wp_ajax_gridspoke_export_logs', array($this, 'ajax_export_logs'));
        
        // Settings page actions
        add_action('admin_post_gridspoke_reset_settings', array($this, 'handle_reset_settings'));
        add_action('admin_post_gridspoke_register_webhook', array($this, 'handle_register_webhook'));
    }
    
    /**
     * Add admin menu pages
     */
    public function add_admin_menu() {
        // Main menu page
        add_menu_page(
            __('GridSpoke SEO', 'gridspoke-seo'),
            __('GridSpoke SEO', 'gridspoke-seo'),
            'manage_options',
            'gridspoke-seo',
            array($this, 'render_dashboard_page'),
            'dashicons-chart-line',
            30
        );
        
        // Dashboard submenu
        add_submenu_page(
            'gridspoke-seo',
            __('Dashboard', 'gridspoke-seo'),
            __('Dashboard', 'gridspoke-seo'),
            'manage_options',
            'gridspoke-seo',
            array($this, 'render_dashboard_page')
        );
        
        // Settings submenu
        add_submenu_page(
            'gridspoke-seo',
            __('Settings', 'gridspoke-seo'),
            __('Settings', 'gridspoke-seo'),
            'manage_options',
            'gridspoke-seo-settings',
            array($this, 'render_settings_page')
        );
        
        // Logs submenu
        add_submenu_page(
            'gridspoke-seo',
            __('Logs', 'gridspoke-seo'),
            __('Logs', 'gridspoke-seo'),
            'manage_options',
            'gridspoke-seo-logs',
            array($this, 'render_logs_page')
        );
        
        // Tools submenu
        add_submenu_page(
            'gridspoke-seo',
            __('Tools', 'gridspoke-seo'),
            __('Tools', 'gridspoke-seo'),
            'manage_options',
            'gridspoke-seo-tools',
            array($this, 'render_tools_page')
        );
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only load on our admin pages
        if (strpos($hook, 'gridspoke-seo') === false) {
            return;
        }
        
        wp_enqueue_script(
            'gridspoke-admin',
            GRIDSPOKE_SEO_PLUGIN_URL . 'admin/assets/js/admin.js',
            array('jquery', 'wp-util'),
            GRIDSPOKE_SEO_VERSION,
            true
        );
        
        wp_localize_script('gridspoke-admin', 'gridspoke_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('gridspoke_admin_nonce'),
            'show_text' => __('Show', 'gridspoke-seo'),
            'hide_text' => __('Hide', 'gridspoke-seo'),
            'testing_text' => __('Testing...', 'gridspoke-seo'),
            'syncing_text' => __('Syncing...', 'gridspoke-seo'),
            'optimizing_text' => __('Optimizing...', 'gridspoke-seo'),
            'cleaning_text' => __('Cleaning...', 'gridspoke-seo'),
            'sync_all_text' => __('Sync All Products', 'gridspoke-seo'),
            'test_auth_text' => __('Test Authentication', 'gridspoke-seo'),
            'bulk_optimize_text' => __('Start Bulk Optimization', 'gridspoke-seo'),
            'run_cleanup_text' => __('Run Cleanup', 'gridspoke-seo'),
            'sync_selected_text' => __('Sync Selected Products', 'gridspoke-seo'),
            'test_webhook_text' => __('Test Webhook', 'gridspoke-seo'),
            'email_password_required' => __('Email and password are required', 'gridspoke-seo'),
            'logged_in_as' => __('Logged in as:', 'gridspoke-seo'),
            'connection_error' => __('Connection error occurred', 'gridspoke-seo'),
            'confirm_sync_all' => __('Are you sure you want to sync all products? This may take some time.', 'gridspoke-seo'),
            'confirm_bulk_optimize' => __('Are you sure you want to optimize all products? This may take some time and consume API credits.', 'gridspoke-seo'),
            'confirm_cleanup' => __('Are you sure you want to run cleanup? This action cannot be undone.', 'gridspoke-seo'),
            'sync_error' => __('Sync failed. Please try again.', 'gridspoke-seo'),
            'optimize_error' => __('Optimization failed. Please try again.', 'gridspoke-seo'),
            'cleanup_error' => __('Cleanup failed. Please try again.', 'gridspoke-seo'),
            'webhook_test_error' => __('Webhook test failed. Please try again.', 'gridspoke-seo'),
            'select_fields_error' => __('Please select at least one field to optimize.', 'gridspoke-seo'),
            'select_cleanup_options' => __('Please select at least one cleanup option.', 'gridspoke-seo')
        ));
        
        wp_enqueue_style(
            'gridspoke-admin',
            GRIDSPOKE_SEO_PLUGIN_URL . 'admin/assets/css/admin.css',
            array(),
            GRIDSPOKE_SEO_VERSION
        );

        // Load WooCommerce specific scripts on product pages
        global $pagenow, $post_type;
        if (class_exists('WooCommerce') &&
            (($pagenow === 'post.php' || $pagenow === 'post-new.php') && $post_type === 'product' ||
             ($pagenow === 'edit.php' && $post_type === 'product'))) {

            wp_enqueue_script(
                'gridspoke-woocommerce-admin',
                GRIDSPOKE_SEO_PLUGIN_URL . 'admin/assets/js/woocommerce-admin.js',
                array('jquery', 'gridspoke-admin'),
                GRIDSPOKE_SEO_VERSION,
                true
            );

            wp_localize_script('gridspoke-woocommerce-admin', 'gridspoke_woo_admin', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('gridspoke_admin_nonce'),
                'optimize_text' => __('Optimize with GridSpoke', 'gridspoke-seo'),
                'sync_text' => __('Sync to GridSpoke', 'gridspoke-seo'),
                'quick_optimize_text' => __('Quick Optimize', 'gridspoke-seo'),
                'quick_optimize_label' => __('GridSpoke SEO:', 'gridspoke-seo'),
                'bulk_optimize_text' => __('Optimize with GridSpoke', 'gridspoke-seo'),
                'optimizing_text' => __('Optimizing...', 'gridspoke-seo'),
                'syncing_text' => __('Syncing...', 'gridspoke-seo'),
                'job_id_text' => __('Job ID:', 'gridspoke-seo'),
                'gridspoke_id_text' => __('GridSpoke ID:', 'gridspoke-seo'),
                'optimization_error' => __('Optimization failed. Please try again.', 'gridspoke-seo'),
                'sync_error' => __('Sync failed. Please try again.', 'gridspoke-seo'),
                'select_fields_error' => __('Please select at least one field to optimize.', 'gridspoke-seo'),
                'select_products_error' => __('Please select at least one product.', 'gridspoke-seo'),
                'confirm_bulk_optimize_products' => __('Are you sure you want to optimize %d selected products?', 'gridspoke-seo'),
                'bulk_optimizing_text' => __('Optimizing selected products...', 'gridspoke-seo'),
                'bulk_optimization_error' => __('Bulk optimization failed. Please try again.', 'gridspoke-seo'),
                'optimization_completed' => __('Optimization completed successfully!', 'gridspoke-seo'),
                'optimization_failed' => __('Optimization failed.', 'gridspoke-seo'),
                'status_optimized' => __('Optimized with GridSpoke', 'gridspoke-seo'),
                'status_pending' => __('Optimization pending', 'gridspoke-seo'),
                'status_error' => __('Optimization error', 'gridspoke-seo'),
                'status_none' => __('Not optimized', 'gridspoke-seo')
            ));
        }
        
        // Chart.js for dashboard
        if ($hook === 'toplevel_page_gridspoke-seo') {
            wp_enqueue_script(
                'chartjs',
                'https://cdn.jsdelivr.net/npm/chart.js',
                array(),
                '3.9.1',
                true
            );
        }
    }
    
    /**
     * Render dashboard page
     */
    public function render_dashboard_page() {
        $api_client = GridSpoke_API_Client::get_instance();
        $webhook_handler = GridSpoke_Webhook_Handler::get_instance();
        $logger = GridSpoke_Logger::get_instance();
        
        // Get statistics
        $optimization_stats = $webhook_handler->get_webhook_stats();
        $log_stats = $logger->get_log_stats();
        
        include GRIDSPOKE_SEO_PLUGIN_PATH . 'admin/views/dashboard.php';
    }
    
    /**
     * Render settings page
     */
    public function render_settings_page() {
        // Handle form submission
        if (isset($_POST['submit']) && wp_verify_nonce($_POST['_wpnonce'], 'gridspoke_seo_settings_group-options')) {
            // WordPress will handle the settings saving automatically
            add_settings_error(
                'gridspoke_seo_settings',
                'settings_updated',
                __('Settings saved successfully!', 'gridspoke-seo'),
                'updated'
            );
        }
        
        include GRIDSPOKE_SEO_PLUGIN_PATH . 'admin/views/settings.php';
    }
    
    /**
     * Render logs page
     */
    public function render_logs_page() {
        $logger = GridSpoke_Logger::get_instance();
        
        // Get filter parameters
        $level = sanitize_text_field($_GET['level'] ?? '');
        $search = sanitize_text_field($_GET['search'] ?? '');
        $page = max(1, intval($_GET['paged'] ?? 1));
        $per_page = 50;
        
        // Get logs
        $logs = $logger->get_logs(array(
            'level' => $level,
            'search' => $search,
            'limit' => $per_page,
            'offset' => ($page - 1) * $per_page
        ));
        
        // Get total count for pagination
        global $wpdb;
        $table_name = $wpdb->prefix . 'gridspoke_logs';
        $where_conditions = array('1=1');
        $where_values = array();
        
        if (!empty($level)) {
            $where_conditions[] = 'level = %s';
            $where_values[] = $level;
        }
        
        if (!empty($search)) {
            $where_conditions[] = 'message LIKE %s';
            $where_values[] = '%' . $wpdb->esc_like($search) . '%';
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        $count_sql = "SELECT COUNT(*) FROM $table_name WHERE $where_clause";
        
        if (!empty($where_values)) {
            $count_sql = $wpdb->prepare($count_sql, $where_values);
        }
        
        $total_logs = $wpdb->get_var($count_sql);
        
        include GRIDSPOKE_SEO_PLUGIN_PATH . 'admin/views/logs.php';
    }
    
    /**
     * Render tools page
     */
    public function render_tools_page() {
        include GRIDSPOKE_SEO_PLUGIN_PATH . 'admin/views/tools.php';
    }
    
    /**
     * Add dashboard widget
     */
    public function add_dashboard_widget() {
        wp_add_dashboard_widget(
            'gridspoke_seo_widget',
            __('GridSpoke SEO Optimization Status', 'gridspoke-seo'),
            array($this, 'render_dashboard_widget')
        );
    }
    
    /**
     * Render dashboard widget
     */
    public function render_dashboard_widget() {
        $webhook_handler = GridSpoke_Webhook_Handler::get_instance();
        $stats = $webhook_handler->get_webhook_stats();
        
        echo '<div class="gridspoke-dashboard-widget">';
        
        echo '<div class="gridspoke-stats-grid">';
        
        echo '<div class="gridspoke-stat-item">';
        echo '<span class="gridspoke-stat-number">' . esc_html($stats['completed_optimizations']) . '</span>';
        echo '<span class="gridspoke-stat-label">' . __('Completed Optimizations', 'gridspoke-seo') . '</span>';
        echo '</div>';
        
        echo '<div class="gridspoke-stat-item">';
        echo '<span class="gridspoke-stat-number">' . esc_html($stats['pending_optimizations']) . '</span>';
        echo '<span class="gridspoke-stat-label">' . __('Pending Optimizations', 'gridspoke-seo') . '</span>';
        echo '</div>';
        
        echo '<div class="gridspoke-stat-item">';
        echo '<span class="gridspoke-stat-number">' . esc_html($stats['failed_optimizations']) . '</span>';
        echo '<span class="gridspoke-stat-label">' . __('Failed Optimizations', 'gridspoke-seo') . '</span>';
        echo '</div>';
        
        echo '</div>';
        
        if (!empty($stats['last_optimization'])) {
            echo '<p class="gridspoke-last-activity">';
            echo '<strong>' . __('Last Activity:', 'gridspoke-seo') . '</strong> ';
            echo esc_html(human_time_diff(strtotime($stats['last_optimization']))) . ' ' . __('ago', 'gridspoke-seo');
            echo '</p>';
        }
        
        echo '<div class="gridspoke-widget-actions">';
        echo '<a href="' . esc_url(admin_url('admin.php?page=gridspoke-seo')) . '" class="button button-primary">';
        echo __('View Dashboard', 'gridspoke-seo');
        echo '</a>';
        echo '</div>';
        
        echo '</div>';
    }
    
    /**
     * Handle admin actions
     */
    public function handle_admin_actions() {
        // Handle settings page actions that need to redirect
        if (isset($_GET['gridspoke_action'])) {
            $action = sanitize_text_field($_GET['gridspoke_action']);
            
            switch ($action) {
                case 'test_connection':
                    $this->handle_test_connection_redirect();
                    break;
            }
        }
    }
    
    /**
     * Handle test connection with redirect
     */
    private function handle_test_connection_redirect() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'gridspoke-seo'));
        }
        
        $api_client = GridSpoke_API_Client::get_instance();
        $result = $api_client->test_connection();
        
        $message_type = $result['success'] ? 'success' : 'error';
        $redirect_url = add_query_arg(array(
            'page' => 'gridspoke-seo-settings',
            'gridspoke_message' => urlencode($result['message']),
            'gridspoke_message_type' => $message_type
        ), admin_url('admin.php'));
        
        wp_redirect($redirect_url);
        exit;
    }
    
    /**
     * AJAX: Test API connection
     */
    public function ajax_test_connection() {
        check_ajax_referer('gridspoke_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'gridspoke-seo')));
        }
        
        $api_client = GridSpoke_API_Client::get_instance();
        $result = $api_client->test_connection();
        
        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }

    /**
     * AJAX: Test authentication
     */
    public function ajax_test_authentication() {
        check_ajax_referer('gridspoke_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'gridspoke-seo')));
        }

        $email = sanitize_email($_POST['email'] ?? '');
        $password = $_POST['password'] ?? '';

        if (empty($email) || empty($password)) {
            wp_send_json_error(array('message' => __('Email and password are required', 'gridspoke-seo')));
        }

        $api_client = GridSpoke_API_Client::get_instance();
        $result = $api_client->authenticate($email, $password);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }

    /**
     * AJAX: Sync all products
     */
    public function ajax_sync_all_products() {
        check_ajax_referer('gridspoke_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'gridspoke-seo')));
        }
        
        $results = array();
        
        // Sync WooCommerce products
        if (class_exists('WooCommerce')) {
            $woo_integration = GridSpoke_WooCommerce_Integration::get_instance();
            $woo_result = $woo_integration->sync_products();
            $results['woocommerce'] = $woo_result !== false;
        }
        
        // Sync SureCart products
        if (defined('SURECART_PLUGIN_FILE')) {
            $sure_integration = GridSpoke_SureCart_Integration::get_instance();
            $sure_result = $sure_integration->sync_products();
            $results['surecart'] = $sure_result !== false;
        }
        
        $success = !empty(array_filter($results));
        
        if ($success) {
            wp_send_json_success(array(
                'message' => __('Products synced successfully', 'gridspoke-seo'),
                'results' => $results
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to sync products', 'gridspoke-seo'),
                'results' => $results
            ));
        }
    }
    
    /**
     * AJAX: Get optimization statistics
     */
    public function ajax_get_optimization_stats() {
        check_ajax_referer('gridspoke_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'gridspoke-seo')));
        }
        
        $webhook_handler = GridSpoke_Webhook_Handler::get_instance();
        $stats = $webhook_handler->get_webhook_stats();
        
        wp_send_json_success($stats);
    }
    
    /**
     * AJAX: Clear logs
     */
    public function ajax_clear_logs() {
        check_ajax_referer('gridspoke_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'gridspoke-seo')));
        }
        
        $level = sanitize_text_field($_POST['level'] ?? '');
        
        $logger = GridSpoke_Logger::get_instance();
        $deleted = $logger->clear_logs($level);
        
        wp_send_json_success(array(
            'message' => sprintf(__('Cleared %d log entries', 'gridspoke-seo'), $deleted),
            'deleted' => $deleted
        ));
    }
    
    /**
     * AJAX: Export logs
     */
    public function ajax_export_logs() {
        check_ajax_referer('gridspoke_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'gridspoke-seo')));
        }
        
        $level = sanitize_text_field($_POST['level'] ?? '');
        $start_date = sanitize_text_field($_POST['start_date'] ?? '');
        $end_date = sanitize_text_field($_POST['end_date'] ?? '');
        
        $args = array();
        if (!empty($level)) $args['level'] = $level;
        if (!empty($start_date)) $args['start_date'] = $start_date;
        if (!empty($end_date)) $args['end_date'] = $end_date;
        
        $logger = GridSpoke_Logger::get_instance();
        $export_result = $logger->export_logs_csv($args);
        
        if ($export_result) {
            wp_send_json_success(array(
                'message' => sprintf(__('Exported %d log entries', 'gridspoke-seo'), $export_result['count']),
                'download_url' => $export_result['url'],
                'filename' => $export_result['filename']
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Failed to export logs', 'gridspoke-seo')
            ));
        }
    }
    
    /**
     * Handle reset settings
     */
    public function handle_reset_settings() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'gridspoke-seo'));
        }
        
        check_admin_referer('gridspoke_reset_settings');
        
        $settings = GridSpoke_Settings::get_instance();
        $settings->reset_settings();
        
        $redirect_url = add_query_arg(array(
            'page' => 'gridspoke-seo-settings',
            'gridspoke_message' => urlencode(__('Settings reset to defaults', 'gridspoke-seo')),
            'gridspoke_message_type' => 'success'
        ), admin_url('admin.php'));
        
        wp_redirect($redirect_url);
        exit;
    }
    
    /**
     * Handle webhook registration
     */
    public function handle_register_webhook() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Insufficient permissions', 'gridspoke-seo'));
        }
        
        check_admin_referer('gridspoke_register_webhook');
        
        $api_client = GridSpoke_API_Client::get_instance();
        $success = $api_client->register_webhook();
        
        $message = $success 
            ? __('Webhook registered successfully', 'gridspoke-seo')
            : __('Failed to register webhook', 'gridspoke-seo');
        
        $message_type = $success ? 'success' : 'error';
        
        $redirect_url = add_query_arg(array(
            'page' => 'gridspoke-seo-tools',
            'gridspoke_message' => urlencode($message),
            'gridspoke_message_type' => $message_type
        ), admin_url('admin.php'));
        
        wp_redirect($redirect_url);
        exit;
    }
    
    /**
     * Display admin notices for our pages
     */
    public static function display_admin_message() {
        if (isset($_GET['gridspoke_message']) && isset($_GET['gridspoke_message_type'])) {
            $message = urldecode($_GET['gridspoke_message']);
            $type = sanitize_text_field($_GET['gridspoke_message_type']);
            
            $class = $type === 'success' ? 'notice-success' : 'notice-error';
            
            echo '<div class="notice ' . esc_attr($class) . ' is-dismissible">';
            echo '<p>' . esc_html($message) . '</p>';
            echo '</div>';
        }
    }
    
    /**
     * AJAX: Sync single product
     */
    public function ajax_sync_single_product() {
        check_ajax_referer('gridspoke_admin_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'gridspoke-seo')));
        }

        $product_id = intval($_POST['product_id'] ?? 0);

        if (!$product_id) {
            wp_send_json_error(array('message' => __('Invalid product ID', 'gridspoke-seo')));
        }

        // Get product data
        if (class_exists('WooCommerce')) {
            $woo_integration = GridSpoke_WooCommerce_Integration::get_instance();
            $result = $woo_integration->sync_single_product($product_id);
        } else {
            wp_send_json_error(array('message' => __('WooCommerce not available', 'gridspoke-seo')));
        }

        if ($result) {
            wp_send_json_success(array(
                'message' => __('Product synced successfully', 'gridspoke-seo'),
                'gridspoke_id' => $result['gridspoke_id'] ?? null
            ));
        } else {
            wp_send_json_error(array('message' => __('Failed to sync product', 'gridspoke-seo')));
        }
    }

    /**
     * AJAX: Optimize single product
     */
    public function ajax_optimize_single_product() {
        check_ajax_referer('gridspoke_admin_nonce', 'nonce');

        if (!current_user_can('edit_posts')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'gridspoke-seo')));
        }

        $product_id = intval($_POST['product_id'] ?? 0);
        $fields = array_map('sanitize_text_field', $_POST['fields'] ?? array());

        if (!$product_id) {
            wp_send_json_error(array('message' => __('Invalid product ID', 'gridspoke-seo')));
        }

        if (empty($fields)) {
            wp_send_json_error(array('message' => __('No optimization fields selected', 'gridspoke-seo')));
        }

        $api_client = GridSpoke_API_Client::get_instance();
        $result = $api_client->request_optimization(array($product_id), array(
            'optimization_types' => $fields
        ));

        if ($result) {
            wp_send_json_success(array(
                'message' => __('Optimization requested successfully', 'gridspoke-seo'),
                'job_id' => $result['job_id'] ?? null
            ));
        } else {
            wp_send_json_error(array('message' => __('Failed to request optimization', 'gridspoke-seo')));
        }
    }

    /**
     * Get current page slug
     */
    private function get_current_page() {
        return $_GET['page'] ?? '';
    }
    
    /**
     * Check if we're on a GridSpoke admin page
     */
    private function is_gridspoke_page() {
        $page = $this->get_current_page();
        return strpos($page, 'gridspoke-seo') === 0;
    }

    /**
     * AJAX: Get optimization statistics
     */
    public function ajax_get_optimization_stats() {
        check_ajax_referer('gridspoke_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'gridspoke-seo')));
        }

        // Get stats from database or API
        $stats = array(
            'completed_optimizations' => get_option('gridspoke_completed_optimizations', 0),
            'pending_optimizations' => get_option('gridspoke_pending_optimizations', 0),
            'failed_optimizations' => get_option('gridspoke_failed_optimizations', 0),
            'total_optimizations' => get_option('gridspoke_total_optimizations', 0)
        );

        wp_send_json_success($stats);
    }

    /**
     * AJAX: Clear logs
     */
    public function ajax_clear_logs() {
        check_ajax_referer('gridspoke_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'gridspoke-seo')));
        }

        $level = sanitize_text_field($_POST['level'] ?? '');

        $logger = GridSpoke_Logger::get_instance();
        $result = $logger->clear_logs($level);

        if ($result) {
            wp_send_json_success(array(
                'message' => empty($level) ?
                    __('All logs cleared successfully', 'gridspoke-seo') :
                    sprintf(__('%s logs cleared successfully', 'gridspoke-seo'), ucfirst($level))
            ));
        } else {
            wp_send_json_error(array('message' => __('Failed to clear logs', 'gridspoke-seo')));
        }
    }

    /**
     * AJAX: Export logs
     */
    public function ajax_export_logs() {
        check_ajax_referer('gridspoke_admin_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Insufficient permissions', 'gridspoke-seo')));
        }

        $level = sanitize_text_field($_POST['level'] ?? '');
        $search = sanitize_text_field($_POST['search'] ?? '');

        $logger = GridSpoke_Logger::get_instance();
        $export_result = $logger->export_logs($level, $search);

        if ($export_result) {
            wp_send_json_success($export_result);
        } else {
            wp_send_json_error(array('message' => __('Failed to export logs', 'gridspoke-seo')));
        }
    }
}
