<?php
/**
 * Tools page template
 */

if (!defined('ABSPATH')) {
    exit;
}

// Display admin messages
GridSpoke_Admin::display_admin_message();

$settings = GridSpoke_SEO_Connector::get_settings();
$api_configured = !empty($settings['api_key']) && !empty($settings['api_endpoint']);
?>

<div class="wrap">
    <h1><?php esc_html_e('GridSpoke SEO Tools', 'gridspoke-seo'); ?></h1>
    
    <div class="gridspoke-tools-grid">
        
        <!-- API Tools -->
        <div class="gridspoke-tool-card">
            <h2><?php esc_html_e('API Tools', 'gridspoke-seo'); ?></h2>
            
            <div class="gridspoke-tool-section">
                <h3><?php esc_html_e('Connection Testing', 'gridspoke-seo'); ?></h3>
                <p><?php esc_html_e('Test your connection to the GridSpoke API and verify your credentials.', 'gridspoke-seo'); ?></p>
                
                <button type="button" id="test-api-connection" class="button button-primary" <?php disabled(!$api_configured); ?>>
                    <?php esc_html_e('Test API Connection', 'gridspoke-seo'); ?>
                </button>
                
                <div id="api-test-result" class="gridspoke-tool-result" style="display: none;"></div>
            </div>
            
            <div class="gridspoke-tool-section">
                <h3><?php esc_html_e('Webhook Management', 'gridspoke-seo'); ?></h3>
                <p><?php esc_html_e('Register your webhook endpoint with GridSpoke to receive optimization completion notifications.', 'gridspoke-seo'); ?></p>
                
                <div class="gridspoke-webhook-info">
                    <strong><?php esc_html_e('Webhook URL:', 'gridspoke-seo'); ?></strong>
                    <code><?php echo esc_html(rest_url('gridspoke-seo/v1/webhook')); ?></code>
                </div>
                
                <div class="gridspoke-webhook-secret">
                    <strong><?php esc_html_e('Webhook Secret:', 'gridspoke-seo'); ?></strong>
                    <code id="webhook-secret"><?php echo esc_html(substr($settings['webhook_secret'] ?? '', 0, 8) . '...'); ?></code>
                    <button type="button" id="show-webhook-secret" class="button button-small">
                        <?php esc_html_e('Show Full Secret', 'gridspoke-seo'); ?>
                    </button>
                </div>
                
                <div class="gridspoke-tool-actions">
                    <a href="<?php echo esc_url(wp_nonce_url(admin_url('admin-post.php?action=gridspoke_register_webhook'), 'gridspoke_register_webhook')); ?>" 
                       class="button button-secondary" <?php disabled(!$api_configured); ?>>
                        <?php esc_html_e('Register Webhook', 'gridspoke-seo'); ?>
                    </a>
                    
                    <button type="button" id="test-webhook" class="button button-secondary" <?php disabled(!$api_configured); ?>>
                        <?php esc_html_e('Test Webhook', 'gridspoke-seo'); ?>
                    </button>
                </div>
                
                <div id="webhook-test-result" class="gridspoke-tool-result" style="display: none;"></div>
            </div>
        </div>
        
        <!-- Product Sync Tools -->
        <div class="gridspoke-tool-card">
            <h2><?php esc_html_e('Product Sync Tools', 'gridspoke-seo'); ?></h2>
            
            <div class="gridspoke-tool-section">
                <h3><?php esc_html_e('Bulk Product Sync', 'gridspoke-seo'); ?></h3>
                <p><?php esc_html_e('Sync all your products with GridSpoke. This may take some time for large catalogs.', 'gridspoke-seo'); ?></p>
                
                <div class="gridspoke-sync-stats">
                    <?php if (class_exists('WooCommerce')): ?>
                        <?php
                        $woo_products = wc_get_products(array(
                            'status' => 'publish',
                            'limit' => -1,
                            'return' => 'ids'
                        ));
                        ?>
                        <p><strong><?php esc_html_e('WooCommerce Products:', 'gridspoke-seo'); ?></strong> <?php echo count($woo_products); ?></p>
                    <?php endif; ?>
                    
                    <?php if (defined('SURECART_PLUGIN_FILE')): ?>
                        <p><strong><?php esc_html_e('SureCart Products:', 'gridspoke-seo'); ?></strong> <span id="surecart-product-count">-</span></p>
                    <?php endif; ?>
                </div>
                
                <button type="button" id="sync-all-products" class="button button-primary" <?php disabled(!$api_configured); ?>>
                    <?php esc_html_e('Sync All Products', 'gridspoke-seo'); ?>
                </button>
                
                <div id="sync-progress" class="gridspoke-progress-bar" style="display: none;">
                    <div class="gridspoke-progress-fill"></div>
                    <span class="gridspoke-progress-text">0%</span>
                </div>
                
                <div id="sync-result" class="gridspoke-tool-result" style="display: none;"></div>
            </div>
            
            <div class="gridspoke-tool-section">
                <h3><?php esc_html_e('Selective Sync', 'gridspoke-seo'); ?></h3>
                <p><?php esc_html_e('Sync specific product categories or date ranges.', 'gridspoke-seo'); ?></p>
                
                <div class="gridspoke-selective-sync-form">
                    <div class="gridspoke-form-row">
                        <label for="sync-category"><?php esc_html_e('Category:', 'gridspoke-seo'); ?></label>
                        <select id="sync-category" name="sync-category">
                            <option value=""><?php esc_html_e('All Categories', 'gridspoke-seo'); ?></option>
                            <?php if (class_exists('WooCommerce')): ?>
                                <?php
                                $categories = get_terms(array(
                                    'taxonomy' => 'product_cat',
                                    'hide_empty' => false
                                ));
                                foreach ($categories as $category) {
                                    echo '<option value="' . esc_attr($category->term_id) . '">' . esc_html($category->name) . '</option>';
                                }
                                ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    
                    <div class="gridspoke-form-row">
                        <label for="sync-date-from"><?php esc_html_e('From Date:', 'gridspoke-seo'); ?></label>
                        <input type="date" id="sync-date-from" name="sync-date-from" />
                        
                        <label for="sync-date-to"><?php esc_html_e('To Date:', 'gridspoke-seo'); ?></label>
                        <input type="date" id="sync-date-to" name="sync-date-to" />
                    </div>
                    
                    <button type="button" id="sync-selective" class="button button-secondary" <?php disabled(!$api_configured); ?>>
                        <?php esc_html_e('Sync Selected Products', 'gridspoke-seo'); ?>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Optimization Tools -->
        <div class="gridspoke-tool-card">
            <h2><?php esc_html_e('Optimization Tools', 'gridspoke-seo'); ?></h2>
            
            <div class="gridspoke-tool-section">
                <h3><?php esc_html_e('Bulk Optimization', 'gridspoke-seo'); ?></h3>
                <p><?php esc_html_e('Request optimization for multiple products at once.', 'gridspoke-seo'); ?></p>
                
                <div class="gridspoke-bulk-optimize-form">
                    <div class="gridspoke-form-row">
                        <label for="optimize-fields"><?php esc_html_e('Fields to Optimize:', 'gridspoke-seo'); ?></label>
                        <div class="gridspoke-checkbox-group">
                            <label><input type="checkbox" name="optimize-fields[]" value="title" checked> <?php esc_html_e('Title', 'gridspoke-seo'); ?></label>
                            <label><input type="checkbox" name="optimize-fields[]" value="description" checked> <?php esc_html_e('Description', 'gridspoke-seo'); ?></label>
                            <label><input type="checkbox" name="optimize-fields[]" value="meta_description" checked> <?php esc_html_e('Meta Description', 'gridspoke-seo'); ?></label>
                            <label><input type="checkbox" name="optimize-fields[]" value="image_alt"> <?php esc_html_e('Image Alt Text', 'gridspoke-seo'); ?></label>
                        </div>
                    </div>
                    
                    <div class="gridspoke-form-row">
                        <label for="optimize-priority"><?php esc_html_e('Priority:', 'gridspoke-seo'); ?></label>
                        <select id="optimize-priority" name="optimize-priority">
                            <option value="low"><?php esc_html_e('Low (Bulk processing)', 'gridspoke-seo'); ?></option>
                            <option value="normal" selected><?php esc_html_e('Normal (Standard queue)', 'gridspoke-seo'); ?></option>
                            <option value="high"><?php esc_html_e('High (Fast processing)', 'gridspoke-seo'); ?></option>
                        </select>
                    </div>
                    
                    <button type="button" id="bulk-optimize" class="button button-primary" <?php disabled(!$api_configured); ?>>
                        <?php esc_html_e('Start Bulk Optimization', 'gridspoke-seo'); ?>
                    </button>
                </div>
                
                <div id="optimize-result" class="gridspoke-tool-result" style="display: none;"></div>
            </div>
            
            <div class="gridspoke-tool-section">
                <h3><?php esc_html_e('Optimization Status', 'gridspoke-seo'); ?></h3>
                <p><?php esc_html_e('Check the status of your optimization jobs.', 'gridspoke-seo'); ?></p>
                
                <button type="button" id="check-optimization-status" class="button button-secondary" <?php disabled(!$api_configured); ?>>
                    <?php esc_html_e('Check Status', 'gridspoke-seo'); ?>
                </button>
                
                <div id="optimization-status-result" class="gridspoke-tool-result" style="display: none;"></div>
            </div>
        </div>
        
        <!-- Maintenance Tools -->
        <div class="gridspoke-tool-card">
            <h2><?php esc_html_e('Maintenance Tools', 'gridspoke-seo'); ?></h2>
            
            <div class="gridspoke-tool-section">
                <h3><?php esc_html_e('Database Cleanup', 'gridspoke-seo'); ?></h3>
                <p><?php esc_html_e('Clean up old optimization records and logs to free up database space.', 'gridspoke-seo'); ?></p>
                
                <div class="gridspoke-cleanup-options">
                    <label>
                        <input type="checkbox" id="cleanup-logs" checked>
                        <?php esc_html_e('Clean up logs older than 30 days', 'gridspoke-seo'); ?>
                    </label>
                    
                    <label>
                        <input type="checkbox" id="cleanup-optimizations">
                        <?php esc_html_e('Clean up completed optimization records older than 90 days', 'gridspoke-seo'); ?>
                    </label>
                </div>
                
                <button type="button" id="run-cleanup" class="button button-secondary">
                    <?php esc_html_e('Run Cleanup', 'gridspoke-seo'); ?>
                </button>
                
                <div id="cleanup-result" class="gridspoke-tool-result" style="display: none;"></div>
            </div>
            
            <div class="gridspoke-tool-section">
                <h3><?php esc_html_e('System Information', 'gridspoke-seo'); ?></h3>
                <p><?php esc_html_e('View system information for troubleshooting.', 'gridspoke-seo'); ?></p>
                
                <button type="button" id="show-system-info" class="button button-secondary">
                    <?php esc_html_e('Show System Info', 'gridspoke-seo'); ?>
                </button>
                
                <div id="system-info" class="gridspoke-system-info" style="display: none;">
                    <h4><?php esc_html_e('WordPress Information', 'gridspoke-seo'); ?></h4>
                    <ul>
                        <li><strong><?php esc_html_e('WordPress Version:', 'gridspoke-seo'); ?></strong> <?php echo esc_html(get_bloginfo('version')); ?></li>
                        <li><strong><?php esc_html_e('PHP Version:', 'gridspoke-seo'); ?></strong> <?php echo esc_html(PHP_VERSION); ?></li>
                        <li><strong><?php esc_html_e('Plugin Version:', 'gridspoke-seo'); ?></strong> <?php echo esc_html(GRIDSPOKE_SEO_VERSION); ?></li>
                        <li><strong><?php esc_html_e('Site URL:', 'gridspoke-seo'); ?></strong> <?php echo esc_html(home_url()); ?></li>
                        <li><strong><?php esc_html_e('REST API URL:', 'gridspoke-seo'); ?></strong> <?php echo esc_html(rest_url('gridspoke-seo/v1/webhook')); ?></li>
                    </ul>
                    
                    <h4><?php esc_html_e('Plugin Status', 'gridspoke-seo'); ?></h4>
                    <ul>
                        <li><strong><?php esc_html_e('WooCommerce:', 'gridspoke-seo'); ?></strong> <?php echo class_exists('WooCommerce') ? esc_html__('Active', 'gridspoke-seo') : esc_html__('Not Installed', 'gridspoke-seo'); ?></li>
                        <li><strong><?php esc_html_e('SureCart:', 'gridspoke-seo'); ?></strong> <?php echo defined('SURECART_PLUGIN_FILE') ? esc_html__('Active', 'gridspoke-seo') : esc_html__('Not Installed', 'gridspoke-seo'); ?></li>
                        <li><strong><?php esc_html_e('API Configured:', 'gridspoke-seo'); ?></strong> <?php echo $api_configured ? esc_html__('Yes', 'gridspoke-seo') : esc_html__('No', 'gridspoke-seo'); ?></li>
                        <li><strong><?php esc_html_e('Auto Sync:', 'gridspoke-seo'); ?></strong> <?php echo !empty($settings['auto_sync']) ? esc_html__('Enabled', 'gridspoke-seo') : esc_html__('Disabled', 'gridspoke-seo'); ?></li>
                    </ul>
                    
                    <button type="button" id="copy-system-info" class="button button-small">
                        <?php esc_html_e('Copy to Clipboard', 'gridspoke-seo'); ?>
                    </button>
                </div>
            </div>
        </div>
        
    </div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Show webhook secret
    $('#show-webhook-secret').on('click', function() {
        var secret = '<?php echo esc_js($settings['webhook_secret'] ?? ''); ?>';
        $('#webhook-secret').text(secret);
        $(this).hide();
    });
    
    // Show system info
    $('#show-system-info').on('click', function() {
        $('#system-info').toggle();
    });
    
    // Copy system info to clipboard
    $('#copy-system-info').on('click', function() {
        var systemInfo = $('#system-info').text();
        navigator.clipboard.writeText(systemInfo).then(function() {
            alert('<?php esc_html_e('System information copied to clipboard', 'gridspoke-seo'); ?>');
        });
    });
    
    // Test API connection
    $('#test-api-connection').on('click', function() {
        var $button = $(this);
        var $result = $('#api-test-result');
        
        $button.prop('disabled', true).text('<?php esc_html_e('Testing...', 'gridspoke-seo'); ?>');
        $result.hide();
        
        $.ajax({
            url: gridspoke_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'gridspoke_test_connection',
                nonce: gridspoke_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    $result.removeClass('error').addClass('success').html('<p>' + response.data.message + '</p>').show();
                } else {
                    $result.removeClass('success').addClass('error').html('<p>' + response.data.message + '</p>').show();
                }
            },
            error: function() {
                $result.removeClass('success').addClass('error').html('<p><?php esc_html_e('Connection test failed', 'gridspoke-seo'); ?></p>').show();
            },
            complete: function() {
                $button.prop('disabled', false).text('<?php esc_html_e('Test API Connection', 'gridspoke-seo'); ?>');
            }
        });
    });
    
    // Sync all products
    $('#sync-all-products').on('click', function() {
        var $button = $(this);
        var $result = $('#sync-result');
        var $progress = $('#sync-progress');
        
        $button.prop('disabled', true).text('<?php esc_html_e('Syncing...', 'gridspoke-seo'); ?>');
        $result.hide();
        $progress.show();
        
        $.ajax({
            url: gridspoke_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'gridspoke_sync_all_products',
                nonce: gridspoke_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    $result.removeClass('error').addClass('success').html('<p>' + response.data.message + '</p>').show();
                } else {
                    $result.removeClass('success').addClass('error').html('<p>' + response.data.message + '</p>').show();
                }
            },
            error: function() {
                $result.removeClass('success').addClass('error').html('<p><?php esc_html_e('Sync failed', 'gridspoke-seo'); ?></p>').show();
            },
            complete: function() {
                $button.prop('disabled', false).text('<?php esc_html_e('Sync All Products', 'gridspoke-seo'); ?>');
                $progress.hide();
            }
        });
    });
});
</script>
