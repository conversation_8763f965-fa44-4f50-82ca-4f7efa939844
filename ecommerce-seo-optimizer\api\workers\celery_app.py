"""Celery application initialization for GridSpoke.
Provides a minimal Celery app so worker/beat containers start successfully.
Extend with task discovery and configuration as deeper task modules are implemented.
"""
from celery import Celery
import os

redis_url = os.getenv("REDIS_URL", "redis://redis:6379/0")
celery_app = Celery(
    "gridspoke",
    broker=redis_url,
    backend=redis_url,
    include=[]  # can append task modules here
)

celery_app.conf.update(
    task_routes={},
    task_default_queue="default",
    result_expires=3600,
)

@celery_app.task
def ping():  # simple sanity task
    return "pong"
